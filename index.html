<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FitTrack - Gym Member Management</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'gym-primary': '#33AADA', // Emerald green
                        'gym-secondary': '#F59E0B', // Amber
                        'gym-accent': '#3B82F6', // Blue
                        'gym-blue': '#337ADE', // Custom blue accent
                        'gym-dark': '#111827', // Gray 900
                        'gym-darker': '#030712', // Gray 950
                    }
                }
            }
        }
    </script>
    <style>
        :root {
            /* Dark theme colors (default) */
            --bg-primary: #111827;
            --bg-secondary: #1f2937;
            --bg-tertiary: #374151;
            --text-primary: #f9fafb;
            --text-secondary: #d1d5db;
            --text-muted: #9ca3af;
            --border-primary: #374151;
            --border-secondary: #4b5563;
            --shadow-color: rgba(0, 0, 0, 0.3);
            --card-bg: #111827;
            --input-bg: #374151;
            --hover-bg: rgba(55, 65, 81, 0.5);

            /* Dark theme subtle gradient colors */
            --gradient-primary-dark: linear-gradient(135deg, #8554F5 0%, #5D42EE 20%, #4268E4 40%, #337ADE 60%, #33AADA 80%, #3FE0D0 100%);
            --gradient-subtle-dark: linear-gradient(135deg, rgba(133, 84, 245, 0.05) 0%, rgba(93, 66, 238, 0.05) 20%, rgba(66, 104, 228, 0.05) 40%, rgba(51, 122, 222, 0.05) 60%, rgba(51, 170, 218, 0.05) 80%, rgba(63, 224, 208, 0.05) 100%);
            --gradient-border-dark: linear-gradient(135deg, rgba(133, 84, 245, 0.1) 0%, rgba(93, 66, 238, 0.1) 20%, rgba(66, 104, 228, 0.1) 40%, rgba(51, 122, 222, 0.1) 60%, rgba(51, 170, 218, 0.1) 80%, rgba(63, 224, 208, 0.1) 100%);
            --gradient-hover-dark: linear-gradient(135deg, rgba(133, 84, 245, 0.08) 0%, rgba(93, 66, 238, 0.08) 20%, rgba(66, 104, 228, 0.08) 40%, rgba(51, 122, 222, 0.08) 60%, rgba(51, 170, 218, 0.08) 80%, rgba(63, 224, 208, 0.08) 100%);
            --gradient-accent-dark: linear-gradient(135deg, rgba(133, 84, 245, 0.15) 0%, rgba(93, 66, 238, 0.15) 20%, rgba(66, 104, 228, 0.15) 40%, rgba(51, 122, 222, 0.15) 60%, rgba(51, 170, 218, 0.15) 80%, rgba(63, 224, 208, 0.15) 100%);
        }

        [data-theme="light"] {
            /* Light theme colors */
            --bg-primary: #ffffff;
            --bg-secondary: #f8fafc;
            --bg-tertiary: #e2e8f0;
            --text-primary: #1e293b;
            --text-secondary: #475569;
            --text-muted: #64748b;
            --border-primary: #e2e8f0;
            --border-secondary: #cbd5e1;
            --shadow-color: rgba(0, 0, 0, 0.1);
            --card-bg: #ffffff;
            --input-bg: #f1f5f9;
            --hover-bg: rgba(241, 245, 249, 0.8);

            /* Vibrant gradient colors */
            --gradient-primary: linear-gradient(135deg, #8554F5 0%, #5D42EE 20%, #4268E4 40%, #337ADE 60%, #33AADA 80%, #3FE0D0 100%);
            --gradient-subtle: linear-gradient(135deg, rgba(133, 84, 245, 0.1) 0%, rgba(93, 66, 238, 0.1) 20%, rgba(66, 104, 228, 0.1) 40%, rgba(51, 122, 222, 0.1) 60%, rgba(51, 170, 218, 0.1) 80%, rgba(63, 224, 208, 0.1) 100%);
            --gradient-border: linear-gradient(135deg, rgba(133, 84, 245, 0.3) 0%, rgba(93, 66, 238, 0.3) 20%, rgba(66, 104, 228, 0.3) 40%, rgba(51, 122, 222, 0.3) 60%, rgba(51, 170, 218, 0.3) 80%, rgba(63, 224, 208, 0.3) 100%);
            --gradient-hover: linear-gradient(135deg, rgba(133, 84, 245, 0.15) 0%, rgba(93, 66, 238, 0.15) 20%, rgba(66, 104, 228, 0.15) 40%, rgba(51, 122, 222, 0.15) 60%, rgba(51, 170, 218, 0.15) 80%, rgba(63, 224, 208, 0.15) 100%);
        }

        /* Theme transition */
        * {
            transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease, box-shadow 0.3s ease;
        }

        /* ===== NOTIFICATION-STYLE TOGGLE BUTTONS ===== */

        /* Base toggle button styles - matching notification button exactly */
        .header-toggle-btn {
            position: relative;
            padding: 8px; /* p-2 equivalent */
            color: #9ca3af; /* text-gray-400 equivalent */
            transition: color 0.3s ease; /* transition-colors equivalent */
            min-width: 44px;
            min-height: 44px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            border: none;
            background: transparent;
        }

        .header-toggle-btn:hover {
            color: white; /* hover:text-white equivalent */
        }

        /* Toggle icon styles - matching notification icon */
        .header-toggle-icon {
            font-size: 16px; /* text-base equivalent */
            transition: all 0.3s ease;
        }

        /* Responsive icon sizing - matching notification button */
        @media (min-width: 640px) {
            .header-toggle-icon {
                font-size: 18px; /* sm:text-lg equivalent */
            }
        }

        /* Active state indicator - badge-like approach similar to notification badge */
        .header-toggle-btn.active::after {
            content: '';
            position: absolute;
            top: 6px;
            right: 6px;
            width: 8px;
            height: 8px;
            background: var(--gym-blue);
            border-radius: 50%;
            border: 2px solid var(--card-bg);
            animation: pulse-badge 2s infinite;
        }

        /* Active state color change */
        .header-toggle-btn.active {
            color: var(--gym-blue);
        }

        /* Subtle pulse animation for active badge */
        @keyframes pulse-badge {
            0%, 100% {
                opacity: 1;
                transform: scale(1);
            }
            50% {
                opacity: 0.8;
                transform: scale(1.1);
            }
        }

        /* Focus states for accessibility - matching notification button pattern */
        .header-toggle-btn:focus-visible {
            outline: 2px solid var(--gym-blue);
            outline-offset: 2px;
        }

        /* Keyboard navigation support */
        .header-toggle-btn:focus:not(:focus-visible) {
            outline: none;
        }

        /* Disabled state */
        .header-toggle-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            color: #6b7280; /* text-gray-500 */
        }

        .header-toggle-btn:disabled:hover {
            color: #6b7280; /* text-gray-500 */
        }

        /* Ensure proper stacking context - matching notification button */
        .header-toggle-btn {
            z-index: 1001;
            position: relative;
        }

        /* Theme-specific enhancements */
        [data-theme="dark"] .header-toggle-btn {
            color: #9ca3af; /* text-gray-400 */
        }

        [data-theme="dark"] .header-toggle-btn:hover {
            color: white;
        }

        [data-theme="light"] .header-toggle-btn {
            color: #6b7280; /* text-gray-500 for better contrast in light mode */
        }

        [data-theme="light"] .header-toggle-btn:hover {
            color: #374151; /* text-gray-700 for better contrast */
        }

        [data-theme="light"] .header-toggle-btn.active {
            color: var(--gym-blue);
        }

        /* Navigation link styles */
        .nav-link {
            color: var(--text-secondary);
        }

        .nav-link:hover {
            color: var(--text-primary);
            background-color: var(--hover-bg);
        }

        .nav-link.active {
            color: var(--text-primary);
            background-color: var(--hover-bg);
        }

        /* Input placeholder styles */
        input::placeholder {
            color: var(--text-muted);
        }

        /* Custom scrollbar for light theme */
        [data-theme="light"] .sidebar-scroll::-webkit-scrollbar-track {
            background: transparent;
        }

        /* Hidden scrollbar by default - Light theme */
        [data-theme="light"] .sidebar-scroll::-webkit-scrollbar-thumb {
            background: transparent;
            transition: background-color 0.3s ease, opacity 0.3s ease;
        }

        [data-theme="light"] .sidebar-scroll::-webkit-scrollbar-thumb:hover {
            background: rgba(100, 116, 139, 0.8);
        }

        /* Show scrollbar when actively scrolling - Light theme */
        [data-theme="light"] .sidebar-scroll.scrolling::-webkit-scrollbar-thumb {
            background: rgba(203, 213, 225, 0.8);
        }

        [data-theme="light"] .sidebar-scroll.scrolling::-webkit-scrollbar-thumb:hover {
            background: rgba(100, 116, 139, 1);
        }

        /* Update fade effects for light theme */
        [data-theme="light"] .sidebar-scroll::before {
            background: linear-gradient(to bottom, var(--card-bg), transparent);
        }

        [data-theme="light"] .sidebar-scroll::after {
            background: linear-gradient(to top, var(--card-bg), transparent);
        }

        /* Light theme gradient enhancements */

        /* Header gradient background */
        [data-theme="light"] header {
            background: var(--gradient-subtle) !important;
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(133, 84, 245, 0.2) !important;
        }

        /* Stats cards with gradient borders and subtle backgrounds */
        [data-theme="light"] .stats-card {
            background: var(--gradient-subtle);
            border: 1px solid transparent;
            background-clip: padding-box;
            position: relative;
        }

        [data-theme="light"] .stats-card::before {
            content: '';
            position: absolute;
            inset: 0;
            padding: 1px;
            background: var(--gradient-border);
            border-radius: inherit;
            mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            mask-composite: xor;
            -webkit-mask-composite: xor;
            pointer-events: none;
        }

        /* Quick action buttons with gradient hover effects */
        [data-theme="light"] .quick-action-btn:hover {
            background: var(--gradient-hover) !important;
            transform: translateY(-1px);
            box-shadow: 0 4px 20px rgba(133, 84, 245, 0.2);
        }

        /* Active navigation item gradient accent */
        [data-theme="light"] .nav-link.active {
            background: var(--gradient-subtle) !important;
            border-left: 3px solid #8554F5;
            position: relative;
        }

        [data-theme="light"] .nav-link.active::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 3px;
            background: var(--gradient-primary);
        }



        /* Recent activity cards with subtle gradient */
        [data-theme="light"] .activity-card {
            background: var(--gradient-subtle);
            border: 1px solid rgba(133, 84, 245, 0.1);
        }

        /* Profile avatar gradient border */
        [data-theme="light"] .profile-avatar {
            background: var(--gradient-primary);
            padding: 2px;
        }

        [data-theme="light"] .profile-avatar > div {
            background: white;
            border-radius: inherit;
        }

        /* Notification dropdown gradient accent */
        [data-theme="light"] #notification-dropdown {
            background: rgba(255, 255, 255, 0.95) !important;
            border: 1px solid rgba(133, 84, 245, 0.2) !important;
            backdrop-filter: blur(10px);
            box-shadow: 0 20px 25px -5px rgba(133, 84, 245, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.1);
        }

        /* Add subtle gradient overlay for light theme notification dropdown */
        [data-theme="light"] #notification-dropdown::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--gradient-subtle);
            pointer-events: none;
            border-radius: inherit;
            opacity: 0.3;
        }

        /* Ensure notification dropdown content is readable in light mode */
        [data-theme="light"] #notification-dropdown .p-4 {
            position: relative;
            z-index: 1;
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(5px);
        }

        [data-theme="light"] #notification-dropdown #notifications-list {
            position: relative;
            z-index: 1;
            background: rgba(255, 255, 255, 0.9);
        }

        /* Notification items styling for light mode */
        [data-theme="light"] .notification-item {
            background: rgba(255, 255, 255, 0.7) !important;
            border: 1px solid rgba(133, 84, 245, 0.1) !important;
            backdrop-filter: blur(5px);
        }

        [data-theme="light"] .notification-item:hover {
            background: rgba(255, 255, 255, 0.9) !important;
            border-color: rgba(133, 84, 245, 0.2) !important;
        }

        /* Search input gradient focus */
        [data-theme="light"] input:focus {
            box-shadow: 0 0 0 3px rgba(133, 84, 245, 0.1) !important;
            border-color: #8554F5 !important;
        }

        /* Gradient text for special elements */
        [data-theme="light"] .gradient-text {
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 700;
        }

        /* Dark theme gradient enhancements - Subtle and Professional */

        /* Header subtle gradient accent */
        [data-theme="dark"] header {
            background: linear-gradient(135deg, var(--card-bg) 0%, var(--card-bg) 70%, rgba(133, 84, 245, 0.03) 100%) !important;
            border-bottom: 1px solid var(--border-primary) !important;
            position: relative;
        }

        [data-theme="dark"] header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: var(--gradient-border-dark);
            opacity: 0.5;
        }

        /* Stats cards with subtle gradient borders */
        [data-theme="dark"] .stats-card {
            background: var(--card-bg);
            border: 1px solid var(--border-primary);
            position: relative;
            overflow: hidden;
        }

        [data-theme="dark"] .stats-card::before {
            content: '';
            position: absolute;
            inset: 0;
            padding: 1px;
            background: var(--gradient-border-dark);
            border-radius: inherit;
            mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            mask-composite: xor;
            -webkit-mask-composite: xor;
            pointer-events: none;
            opacity: 0.6;
        }

        [data-theme="dark"] .stats-card::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 100%;
            background: var(--gradient-subtle-dark);
            pointer-events: none;
            opacity: 0.3;
        }

        /* Quick action buttons with subtle gradient hover */
        [data-theme="dark"] .quick-action-btn:hover {
            background: var(--gradient-hover-dark) !important;
            border: 1px solid rgba(133, 84, 245, 0.1);
            box-shadow: 0 2px 8px rgba(133, 84, 245, 0.05);
        }

        /* Active navigation item subtle gradient accent */
        [data-theme="dark"] .nav-link.active {
            background: var(--gradient-subtle-dark) !important;
            border-left: 2px solid rgba(133, 84, 245, 0.4);
            position: relative;
        }

        [data-theme="dark"] .nav-link.active::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 2px;
            background: var(--gradient-primary-dark);
            opacity: 0.6;
        }



        /* Recent activity cards with minimal gradient accent */
        [data-theme="dark"] .activity-card {
            background: var(--hover-bg);
            border: 1px solid rgba(133, 84, 245, 0.05);
            position: relative;
        }

        [data-theme="dark"] .activity-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: var(--gradient-border-dark);
            opacity: 0.3;
        }

        /* Profile avatar subtle gradient border */
        [data-theme="dark"] .profile-avatar {
            background: var(--gradient-border-dark);
            padding: 1px;
        }

        /* Notification dropdown subtle gradient accent */
        [data-theme="dark"] #notification-dropdown {
            background: var(--card-bg) !important;
            border: 1px solid var(--border-primary) !important;
            position: relative;
        }

        [data-theme="dark"] #notification-dropdown::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 100%;
            background: var(--gradient-subtle-dark);
            pointer-events: none;
            opacity: 0.2;
            border-radius: inherit;
        }

        /* Notification items styling for dark mode */
        [data-theme="dark"] .notification-item {
            background: var(--hover-bg) !important;
            border: 1px solid rgba(133, 84, 245, 0.05) !important;
            position: relative;
        }

        [data-theme="dark"] .notification-item:hover {
            background: rgba(75, 85, 99, 0.4) !important;
            border-color: rgba(133, 84, 245, 0.1) !important;
        }

        [data-theme="dark"] .notification-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: var(--gradient-border-dark);
            opacity: 0.3;
        }

        /* Search input subtle gradient focus */
        [data-theme="dark"] input:focus {
            box-shadow: 0 0 0 2px rgba(133, 84, 245, 0.1) !important;
            border-color: rgba(133, 84, 245, 0.3) !important;
        }

        /* Gradient text for dark theme - more subtle */
        [data-theme="dark"] .gradient-text {
            background: var(--gradient-primary-dark);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 700;
            opacity: 0.9;
        }

        /* Sidebar subtle gradient enhancement */
        [data-theme="dark"] #sidebar {
            background: linear-gradient(180deg, var(--card-bg) 0%, var(--card-bg) 90%, rgba(133, 84, 245, 0.02) 100%) !important;
            border-right: 1px solid var(--border-primary) !important;
            position: relative;
        }

        [data-theme="dark"] #sidebar::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            bottom: 0;
            width: 1px;
            background: var(--gradient-border-dark);
            opacity: 0.3;
        }

        /* Main content area subtle gradient */
        [data-theme="dark"] main {
            background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-primary) 95%, rgba(133, 84, 245, 0.01) 100%);
        }

        /* Card containers subtle enhancement */
        [data-theme="dark"] .rounded-xl {
            position: relative;
        }

        [data-theme="dark"] .rounded-xl:not(.stats-card):not(.activity-card):not(.quick-action-btn) {
            background: var(--card-bg);
            border: 1px solid var(--border-primary);
        }

        [data-theme="dark"] .rounded-xl:not(.stats-card):not(.activity-card):not(.quick-action-btn)::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: var(--gradient-border-dark);
            opacity: 0.2;
            border-radius: inherit;
        }

        /* Navigation hover effects with subtle gradient */
        [data-theme="dark"] .nav-link:hover:not(.active) {
            background: var(--gradient-subtle-dark) !important;
            border-left: 1px solid rgba(133, 84, 245, 0.2);
        }

        /* Button and interactive element enhancements */
        [data-theme="dark"] button:hover {
            box-shadow: 0 1px 4px rgba(133, 84, 245, 0.05);
        }

        /* Scrollbar gradient enhancement for dark theme */
        [data-theme="dark"] .sidebar-scroll.scrolling::-webkit-scrollbar-thumb {
            background: linear-gradient(180deg, rgba(75, 85, 99, 0.8) 0%, rgba(133, 84, 245, 0.3) 100%) !important;
        }

        /* Mobile menu overlay gradient */
        [data-theme="dark"] #mobile-menu-overlay {
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.5) 0%, rgba(133, 84, 245, 0.1) 100%);
        }

        /* Notification badge subtle gradient */
        [data-theme="dark"] #notification-badge {
            background: var(--gradient-accent-dark);
            border: 1px solid rgba(133, 84, 245, 0.2);
        }

        /* Form elements subtle gradient focus */
        [data-theme="dark"] select:focus,
        [data-theme="dark"] textarea:focus {
            box-shadow: 0 0 0 2px rgba(133, 84, 245, 0.1) !important;
            border-color: rgba(133, 84, 245, 0.3) !important;
        }

        /* Subtle animation for gradient elements */
        [data-theme="dark"] .stats-card,
        [data-theme="dark"] .activity-card,
        [data-theme="dark"] .quick-action-btn {
            transition: all 0.3s ease, box-shadow 0.3s ease;
        }

        /* Hover enhancement for cards */
        [data-theme="dark"] .stats-card:hover::before {
            opacity: 0.8;
        }

        [data-theme="dark"] .stats-card:hover::after {
            opacity: 0.4;
        }

        /* Focus states for accessibility */
        [data-theme="dark"] *:focus-visible {
            outline: 2px solid rgba(133, 84, 245, 0.4);
            outline-offset: 2px;
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="font-sans" style="background-color: var(--bg-primary); color: var(--text-primary);" data-theme="dark">
    <!-- Mobile menu overlay -->
    <div id="mobile-menu-overlay" class="fixed inset-0 bg-black bg-opacity-50 z-40 hidden lg:hidden"></div>

    <!-- Main Layout Container -->
    <div class="flex h-screen">
        <!-- Sidebar -->
        <div id="sidebar" class="fixed inset-y-0 left-0 z-50 w-64 transform -translate-x-full transition-transform duration-300 ease-in-out lg:translate-x-0 lg:relative lg:flex lg:flex-col" style="background-color: var(--card-bg); border-right: 1px solid var(--border-primary);">
            <!-- Fixed Sidebar Header -->
            <div class="flex items-center justify-between h-12 px-4 flex-shrink-0" style="border-bottom: 1px solid var(--border-primary);">
                <div class="flex items-center space-x-2">
                    <div class="w-6 h-6 bg-gym-primary rounded-md flex items-center justify-center">
                        <i class="fas fa-dumbbell text-white text-xs"></i>
                    </div>
                    <span class="gradient-text text-lg font-bold text-white">FitTrack</span>
                </div>
                <button id="close-sidebar" class="lg:hidden text-gray-400 hover:text-white">
                    <i class="fas fa-times text-sm"></i>
                </button>
            </div>

            <!-- Scrollable Navigation Container -->
            <div class="flex-1 overflow-y-auto sidebar-scroll">
                <nav class="mt-3 px-2 pb-3">
            <div class="space-y-0.5">
                <a href="#dashboard" class="nav-link active flex items-center px-2 py-1.5 text-sm font-medium rounded-md transition-colors" style="color: var(--text-primary); background-color: var(--hover-bg);">
                    <i class="fas fa-tachometer-alt w-4 h-4 mr-2"></i>
                    Dashboard
                </a>
                <a href="#members" class="nav-link flex items-center px-2 py-1.5 text-sm font-medium rounded-md transition-colors" style="color: var(--text-secondary);">
                    <i class="fas fa-users w-4 h-4 mr-2"></i>
                    Members
                </a>
                <a href="#registration" class="nav-link flex items-center px-2 py-1.5 text-sm font-medium rounded-md transition-colors">
                    <i class="fas fa-user-plus w-4 h-4 mr-2"></i>
                    Registration
                </a>
                <a href="#checkin" class="nav-link flex items-center px-2 py-1.5 text-sm font-medium rounded-md transition-colors">
                    <i class="fas fa-sign-in-alt w-4 h-4 mr-2"></i>
                    Check-in/Out
                </a>
                <a href="#memberships" class="nav-link flex items-center px-2 py-1.5 text-sm font-medium rounded-md transition-colors">
                    <i class="fas fa-credit-card w-4 h-4 mr-2"></i>
                    Memberships
                </a>
                <a href="#billing" class="nav-link flex items-center px-2 py-1.5 text-sm font-medium rounded-md transition-colors">
                    <i class="fas fa-receipt w-4 h-4 mr-2"></i>
                    Billing
                </a>
                <a href="#schedule" class="nav-link flex items-center px-2 py-1.5 text-sm font-medium rounded-md transition-colors">
                    <i class="fas fa-calendar-alt w-4 h-4 mr-2"></i>
                    Class Schedule
                </a>
                <a href="#statistics" class="nav-link flex items-center px-2 py-1.5 text-sm font-medium rounded-md transition-colors">
                    <i class="fas fa-chart-bar w-4 h-4 mr-2"></i>
                    Statistics
                </a>
            </div>

            <!-- Additional Menu Items for Testing Scroll -->
            <div class="mt-4 pt-3 border-t border-gray-800">
                <div class="space-y-0.5">
                    <a href="#trainers" class="nav-link flex items-center px-2 py-1.5 text-sm font-medium rounded-md transition-colors">
                        <i class="fas fa-user-tie w-4 h-4 mr-2"></i>
                        Trainers
                    </a>
                    <a href="#equipment" class="nav-link flex items-center px-2 py-1.5 text-sm font-medium rounded-md transition-colors">
                        <i class="fas fa-dumbbell w-4 h-4 mr-2"></i>
                        Equipment
                    </a>
                    <a href="#inventory" class="nav-link flex items-center px-2 py-1.5 text-sm font-medium rounded-md transition-colors">
                        <i class="fas fa-boxes w-4 h-4 mr-2"></i>
                        Inventory
                    </a>
                    <a href="#maintenance" class="nav-link flex items-center px-2 py-1.5 text-sm font-medium rounded-md transition-colors">
                        <i class="fas fa-wrench w-4 h-4 mr-2"></i>
                        Maintenance
                    </a>
                    <a href="#reports" class="nav-link flex items-center px-2 py-1.5 text-sm font-medium rounded-md transition-colors">
                        <i class="fas fa-file-alt w-4 h-4 mr-2"></i>
                        Reports
                    </a>
                    <a href="#analytics" class="nav-link flex items-center px-2 py-1.5 text-sm font-medium rounded-md transition-colors">
                        <i class="fas fa-chart-line w-4 h-4 mr-2"></i>
                        Analytics
                    </a>
                    <a href="#notifications-page" class="nav-link flex items-center px-2 py-1.5 text-sm font-medium rounded-md transition-colors">
                        <i class="fas fa-bell w-4 h-4 mr-2"></i>
                        All Notifications
                    </a>
                    <a href="#help" class="nav-link flex items-center px-2 py-1.5 text-sm font-medium rounded-md transition-colors">
                        <i class="fas fa-question-circle w-4 h-4 mr-2"></i>
                        Help & Support
                    </a>
                </div>
            </div>

            <div class="mt-4 pt-3 border-t border-gray-800">
                <div class="space-y-0.5">
                    <a href="#settings" class="nav-link flex items-center px-2 py-1.5 text-sm font-medium rounded-md transition-colors">
                        <i class="fas fa-cog w-4 h-4 mr-2"></i>
                        Settings
                    </a>
                    <a href="#profile" class="nav-link flex items-center px-2 py-1.5 text-sm font-medium rounded-md transition-colors">
                        <i class="fas fa-user-circle w-4 h-4 mr-2"></i>
                        Profile
                    </a>
                    <a href="#logout" class="nav-link flex items-center px-2 py-1.5 text-sm font-medium rounded-md transition-colors text-red-400 hover:text-red-300 hover:bg-red-900/20">
                        <i class="fas fa-sign-out-alt w-4 h-4 mr-2"></i>
                        Logout
                    </a>
                </div>
                </div>
                </nav>
            </div>
        </div>

        <!-- Main content -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Top header -->
            <header class="px-3 py-2 sm:px-4 lg:px-6 flex-shrink-0" style="background-color: var(--card-bg); border-bottom: 1px solid var(--border-primary);">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <button id="open-sidebar" class="lg:hidden text-gray-400 hover:text-white">
                            <i class="fas fa-bars text-lg"></i>
                        </button>
                        <div>
                            <h1 class="gradient-text text-xl font-bold text-white">Dashboard</h1>
                        </div>
                    </div>

                    <div class="flex items-center space-x-2 sm:space-x-4">

                        <!-- Search -->
                        <!-- <div class="hidden sm:block relative">
                            <input type="text" placeholder="Search members..."
                                   class="rounded-lg px-3 py-2 pl-9 text-sm focus:outline-none focus:ring-2 focus:ring-gym-primary focus:border-transparent w-48 lg:w-64"
                                   style="background-color: var(--input-bg); border: 1px solid var(--border-secondary); color: var(--text-primary);"
                                   onchange="this.style.color = 'var(--text-primary)'"
                                   onfocus="this.style.color = 'var(--text-primary)'">
                        </div> -->

                        <!-- Notifications -->
                        <div class="relative">
                            <button id="notification-btn" class="relative p-2 text-gray-400 hover:text-white transition-colors min-w-[44px] min-h-[44px] flex items-center justify-center">
                                <i class="fas fa-bell text-base sm:text-lg"></i>
                                <span id="notification-badge" class="absolute -top-1 -right-1 bg-gym-secondary text-xs text-white rounded-full w-4 h-4 sm:w-5 sm:h-5 flex items-center justify-center text-[10px] sm:text-xs">3</span>
                            </button>

                            <!-- Notification Dropdown -->
                            <div id="notification-dropdown" class="w-80 rounded-xl shadow-2xl hidden" style="background-color: var(--card-bg); border: 1px solid var(--border-primary); position: fixed; z-index: 9999;">
                                <!-- Dropdown Header -->
                                <div class="p-3 sm:p-4" style="border-bottom: 1px solid var(--border-primary);">
                                    <div class="flex items-center justify-between">
                                        <h3 class="text-base sm:text-lg font-semibold" style="color: var(--text-primary);">Notifications</h3>
                                        <button id="mark-all-read" class="text-gym-primary hover:text-gym-primary/80 text-xs sm:text-sm font-medium transition-colors">
                                            Mark all read
                                        </button>
                                    </div>
                                </div>

                                <!-- Notifications List -->
                                <div id="notifications-list" class="max-h-80 sm:max-h-96 overflow-y-auto">
                                    <!-- Notification items will be dynamically inserted here -->
                                </div>

                                <!-- Dropdown Footer -->
                                <div class="p-3 sm:p-4" style="border-top: 1px solid var(--border-primary);">
                                    <button id="view-all-notifications" class="w-full text-center text-gym-primary hover:text-gym-primary/80 text-xs sm:text-sm font-medium transition-colors">
                                        View all notifications
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Profile -->
                        <!-- <div class="flex items-center space-x-2 sm:space-x-3">
                            <div class="profile-avatar w-7 h-7 sm:w-8 sm:h-8 rounded-full flex items-center justify-center" style="background: #10B981;">
                                <div class="w-full h-full bg-gym-primary rounded-full flex items-center justify-center">
                                    <i class="fas fa-user text-white text-xs sm:text-sm"></i>
                                </div>
                            </div>
                            <span class="hidden sm:block text-xs sm:text-sm font-medium text-white">John</span>
                        </div> -->


                        <!-- Theme Toggle -->
                        <div class="relative">
                            <button id="theme-toggle" class="header-toggle-btn" aria-label="Toggle theme" title="Switch theme">
                                <i class="fas fa-moon header-toggle-icon" id="theme-icon"></i>
                            </button>
                        </div>

                        <!-- Fullscreen Toggle -->
                        <div class="relative">
                            <button id="fullscreen-toggle" class="header-toggle-btn" aria-label="Toggle fullscreen mode" title="Toggle fullscreen">
                                <i class="fas fa-expand header-toggle-icon" id="fullscreen-icon"></i>
                            </button>
                        </div>

                    </div>
                </div>
            </header>

            <!-- Main content area -->
            <main class="flex-1 overflow-y-auto p-2 sm:p-3 lg:p-4">
            <!-- Dashboard Section -->
            <div id="dashboard-section" class="section active">
                <!-- Stats Cards -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3 mb-4">
                    <div class="stats-card rounded-lg p-3" style="background-color: var(--card-bg); border: 1px solid var(--border-primary);">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-xs font-medium" style="color: var(--text-muted);">Total Members</p>
                                <p class="text-2xl font-bold mt-0.5" style="color: var(--text-primary);">1,247</p>
                                <p class="text-gym-primary text-xs mt-0.5">
                                    <i class="fas fa-arrow-up mr-1"></i>+12% from last month
                                </p>
                            </div>
                            <div class="w-10 h-10 bg-gym-primary/20 rounded-lg flex items-center justify-center">
                                <i class="fas fa-users text-gym-primary text-lg"></i>
                            </div>
                        </div>
                    </div>

                    <div class="stats-card rounded-lg p-3" style="background-color: var(--card-bg); border: 1px solid var(--border-primary);">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-xs font-medium" style="color: var(--text-muted);">Active Today</p>
                                <p class="text-2xl font-bold mt-0.5" style="color: var(--text-primary);">89</p>
                                <p class="text-gym-secondary text-xs mt-0.5">
                                    <i class="fas fa-arrow-up mr-1"></i>+5% from yesterday
                                </p>
                            </div>
                            <div class="w-10 h-10 bg-gym-secondary/20 rounded-lg flex items-center justify-center">
                                <i class="fas fa-sign-in-alt text-gym-secondary text-lg"></i>
                            </div>
                        </div>
                    </div>

                    <div class="stats-card rounded-lg p-3" style="background-color: var(--card-bg); border: 1px solid var(--border-primary);">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-xs font-medium" style="color: var(--text-muted);">Revenue</p>
                                <p class="text-2xl font-bold mt-0.5" style="color: var(--text-primary);">$24,580</p>
                                <p class="text-gym-accent text-xs mt-0.5">
                                    <i class="fas fa-arrow-up mr-1"></i>+8% from last month
                                </p>
                            </div>
                            <div class="w-10 h-10 bg-gym-accent/20 rounded-lg flex items-center justify-center">
                                <i class="fas fa-dollar-sign text-gym-accent text-lg"></i>
                            </div>
                        </div>
                    </div>

                    <div class="stats-card rounded-lg p-3" style="background-color: var(--card-bg); border: 1px solid var(--border-primary);">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-xs font-medium" style="color: var(--text-muted);">Classes Today</p>
                                <p class="text-2xl font-bold mt-0.5" style="color: var(--text-primary);">12</p>
                                <p class="text-gym-blue text-xs mt-0.5">
                                    <i class="fas fa-check mr-1"></i>All scheduled
                                </p>
                            </div>
                            <div class="w-10 h-10 bg-gym-blue/20 rounded-lg flex items-center justify-center">
                                <i class="fas fa-calendar-check text-gym-blue text-lg"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions & Recent Activity -->
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-4 mb-4">
                    <!-- Quick Actions -->
                    <div class="lg:col-span-1">
                        <div class="rounded-lg p-3" style="background-color: var(--card-bg); border: 1px solid var(--border-primary);">
                            <h3 class="text-base font-semibold mb-2" style="color: var(--text-primary);">Quick Actions</h3>
                            <div class="space-y-2">
                                <button class="quick-action-btn w-full flex items-center justify-between p-2 bg-gym-primary/10 hover:bg-gym-primary/20 rounded-md transition-all duration-300 group">
                                    <div class="flex items-center space-x-2">
                                        <i class="fas fa-user-plus text-gym-primary text-sm"></i>
                                        <span class="text-sm" style="color: var(--text-primary);">Add New Member</span>
                                    </div>
                                    <i class="fas fa-arrow-right group-hover:text-gym-primary transition-colors text-xs" style="color: var(--text-muted);"></i>
                                </button>

                                <button class="quick-action-btn w-full flex items-center justify-between p-2 bg-gym-secondary/10 hover:bg-gym-secondary/20 rounded-md transition-all duration-300 group">
                                    <div class="flex items-center space-x-2">
                                        <i class="fas fa-sign-in-alt text-gym-secondary text-sm"></i>
                                        <span class="text-sm" style="color: var(--text-primary);">Quick Check-in</span>
                                    </div>
                                    <i class="fas fa-arrow-right group-hover:text-gym-secondary transition-colors text-xs" style="color: var(--text-muted);"></i>
                                </button>

                                <button class="quick-action-btn w-full flex items-center justify-between p-2 bg-gym-accent/10 hover:bg-gym-accent/20 rounded-md transition-all duration-300 group">
                                    <div class="flex items-center space-x-2">
                                        <i class="fas fa-calendar-plus text-gym-accent text-sm"></i>
                                        <span class="text-sm" style="color: var(--text-primary);">Schedule Class</span>
                                    </div>
                                    <i class="fas fa-arrow-right group-hover:text-gym-accent transition-colors text-xs" style="color: var(--text-muted);"></i>
                                </button>

                                <button class="quick-action-btn w-full flex items-center justify-between p-2 bg-gym-blue/10 hover:bg-gym-blue/20 rounded-md transition-all duration-300 group">
                                    <div class="flex items-center space-x-2">
                                        <i class="fas fa-chart-line text-gym-blue text-sm"></i>
                                        <span class="text-sm" style="color: var(--text-primary);">View Reports</span>
                                    </div>
                                    <i class="fas fa-arrow-right group-hover:text-gym-blue transition-colors text-xs" style="color: var(--text-muted);"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Activity -->
                    <div class="lg:col-span-2">
                        <div class="rounded-lg p-3" style="background-color: var(--card-bg); border: 1px solid var(--border-primary);">
                            <div class="flex items-center justify-between mb-2">
                                <h3 class="text-base font-semibold" style="color: var(--text-primary);">Recent Activity</h3>
                                <button class="text-gym-primary hover:text-gym-primary/80 text-xs font-medium">View All</button>
                            </div>
                            <div class="space-y-2">
                                <div class="activity-card flex items-center space-x-3 p-2 rounded-md" style="background-color: var(--hover-bg);">
                                    <div class="w-8 h-8 bg-gym-primary rounded-full flex items-center justify-center">
                                        <i class="fas fa-sign-in-alt text-white text-xs"></i>
                                    </div>
                                    <div class="flex-1">
                                        <p class="font-medium text-sm" style="color: var(--text-primary);">John Smith checked in</p>
                                        <p class="text-xs" style="color: var(--text-muted);">Premium member • 2 minutes ago</p>
                                    </div>
                                    <span class="text-gym-primary text-xs font-medium">Active</span>
                                </div>

                                <div class="activity-card flex items-center space-x-3 p-2 rounded-md" style="background-color: var(--hover-bg);">
                                    <div class="w-8 h-8 bg-gym-secondary rounded-full flex items-center justify-center">
                                        <i class="fas fa-user-plus text-white text-xs"></i>
                                    </div>
                                    <div class="flex-1">
                                        <p class="font-medium text-sm" style="color: var(--text-primary);">Sarah Johnson registered</p>
                                        <p class="text-xs" style="color: var(--text-muted);">Basic membership • 15 minutes ago</p>
                                    </div>
                                    <span class="text-gym-secondary text-xs font-medium">New</span>
                                </div>

                                <div class="activity-card flex items-center space-x-3 p-2 rounded-md" style="background-color: var(--hover-bg);">
                                    <div class="w-8 h-8 bg-gym-accent rounded-full flex items-center justify-center">
                                        <i class="fas fa-credit-card text-white text-xs"></i>
                                    </div>
                                    <div class="flex-1">
                                        <p class="font-medium text-sm" style="color: var(--text-primary);">Payment received from Mike Davis</p>
                                        <p class="text-xs" style="color: var(--text-muted);">$89.99 • 1 hour ago</p>
                                    </div>
                                    <span class="text-gym-accent text-xs font-medium">Paid</span>
                                </div>

                                <div class="activity-card flex items-center space-x-3 p-2 rounded-md" style="background-color: var(--hover-bg);">
                                    <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                                        <i class="fas fa-dumbbell text-white text-xs"></i>
                                    </div>
                                    <div class="flex-1">
                                        <p class="font-medium text-sm" style="color: var(--text-primary);">HIIT class completed</p>
                                        <p class="text-xs" style="color: var(--text-muted);">18 attendees • 2 hours ago</p>
                                    </div>
                                    <span class="text-green-400 text-xs font-medium">Complete</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Member Profile Section -->
            <div id="member-profile-section" class="section hidden">
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    <!-- Profile Info -->
                    <div class="lg:col-span-1">
                        <div class="bg-gym-dark rounded-xl p-6 border border-gray-800 mb-6">
                            <div class="text-center">
                                <div class="w-24 h-24 bg-gym-primary rounded-full mx-auto mb-4 flex items-center justify-center">
                                    <i class="fas fa-user text-white text-2xl"></i>
                                </div>
                                <h2 class="text-xl font-bold text-white">John Smith</h2>
                                <p class="text-gray-400">Premium Member</p>
                                <div class="flex items-center justify-center mt-2">
                                    <span class="bg-gym-primary text-white text-xs px-2 py-1 rounded-full">Active</span>
                                </div>
                            </div>

                            <div class="mt-6 space-y-4">
                                <div class="flex items-center justify-between">
                                    <span class="text-gray-400">Member ID</span>
                                    <span class="text-white">#12345</span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="text-gray-400">Join Date</span>
                                    <span class="text-white">Jan 15, 2024</span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="text-gray-400">Phone</span>
                                    <span class="text-white">(*************</span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="text-gray-400">Email</span>
                                    <span class="text-white text-sm"><EMAIL></span>
                                </div>
                            </div>

                            <div class="mt-6 pt-6 border-t border-gray-800">
                                <button class="w-full bg-gym-primary hover:bg-gym-primary/80 text-white py-2 px-4 rounded-lg transition-colors">
                                    Edit Profile
                                </button>
                            </div>
                        </div>

                        <!-- Membership Status -->
                        <div class="bg-gym-dark rounded-xl p-6 border border-gray-800">
                            <h3 class="text-lg font-semibold text-white mb-4">Membership Status</h3>
                            <div class="space-y-4">
                                <div>
                                    <div class="flex items-center justify-between mb-2">
                                        <span class="text-gray-400">Plan</span>
                                        <span class="text-gym-primary font-medium">Premium</span>
                                    </div>
                                    <div class="flex items-center justify-between mb-2">
                                        <span class="text-gray-400">Expires</span>
                                        <span class="text-white">Dec 15, 2024</span>
                                    </div>
                                    <div class="w-full bg-gray-800 rounded-full h-2">
                                        <div class="bg-gym-primary h-2 rounded-full" style="width: 75%"></div>
                                    </div>
                                    <p class="text-xs text-gray-400 mt-1">9 months remaining</p>
                                </div>

                                <div class="pt-4 border-t border-gray-800">
                                    <div class="flex items-center justify-between mb-2">
                                        <span class="text-gray-400">Monthly Fee</span>
                                        <span class="text-white font-medium">$89.99</span>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <span class="text-gray-400">Next Payment</span>
                                        <span class="text-white">Mar 15, 2024</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Activity & Progress -->
                    <div class="lg:col-span-2">
                        <!-- Progress Stats -->
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                            <div class="bg-gym-dark rounded-xl p-6 border border-gray-800">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="text-gray-400 text-sm">This Month</p>
                                        <p class="text-2xl font-bold text-white">18</p>
                                        <p class="text-gym-primary text-sm">Visits</p>
                                    </div>
                                    <i class="fas fa-calendar-check text-gym-primary text-2xl"></i>
                                </div>
                            </div>

                            <div class="bg-gym-dark rounded-xl p-6 border border-gray-800">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="text-gray-400 text-sm">Total Hours</p>
                                        <p class="text-2xl font-bold text-white">47</p>
                                        <p class="text-gym-secondary text-sm">This Month</p>
                                    </div>
                                    <i class="fas fa-clock text-gym-secondary text-2xl"></i>
                                </div>
                            </div>

                            <div class="bg-gym-dark rounded-xl p-6 border border-gray-800">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="text-gray-400 text-sm">Classes</p>
                                        <p class="text-2xl font-bold text-white">12</p>
                                        <p class="text-gym-accent text-sm">Attended</p>
                                    </div>
                                    <i class="fas fa-users text-gym-accent text-2xl"></i>
                                </div>
                            </div>
                        </div>

                        <!-- Recent Visits -->
                        <div class="bg-gym-dark rounded-xl p-6 border border-gray-800 mb-6">
                            <h3 class="text-lg font-semibold text-white mb-4">Recent Visits</h3>
                            <div class="space-y-3">
                                <div class="flex items-center justify-between p-3 bg-gray-800/50 rounded-lg">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-8 h-8 bg-gym-primary rounded-full flex items-center justify-center">
                                            <i class="fas fa-sign-in-alt text-white text-xs"></i>
                                        </div>
                                        <div>
                                            <p class="text-white font-medium">Today</p>
                                            <p class="text-gray-400 text-sm">6:30 AM - 8:15 AM</p>
                                        </div>
                                    </div>
                                    <span class="text-gym-primary text-sm">1h 45m</span>
                                </div>

                                <div class="flex items-center justify-between p-3 bg-gray-800/50 rounded-lg">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-8 h-8 bg-gray-600 rounded-full flex items-center justify-center">
                                            <i class="fas fa-sign-out-alt text-white text-xs"></i>
                                        </div>
                                        <div>
                                            <p class="text-white font-medium">Yesterday</p>
                                            <p class="text-gray-400 text-sm">7:00 PM - 8:30 PM</p>
                                        </div>
                                    </div>
                                    <span class="text-gray-400 text-sm">1h 30m</span>
                                </div>

                                <div class="flex items-center justify-between p-3 bg-gray-800/50 rounded-lg">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-8 h-8 bg-gray-600 rounded-full flex items-center justify-center">
                                            <i class="fas fa-sign-out-alt text-white text-xs"></i>
                                        </div>
                                        <div>
                                            <p class="text-white font-medium">Mar 10</p>
                                            <p class="text-gray-400 text-sm">5:45 AM - 7:00 AM</p>
                                        </div>
                                    </div>
                                    <span class="text-gray-400 text-sm">1h 15m</span>
                                </div>
                            </div>
                        </div>

                        <!-- Goals & Achievements -->
                        <div class="bg-gym-dark rounded-xl p-6 border border-gray-800">
                            <h3 class="text-lg font-semibold text-white mb-4">Goals & Achievements</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div class="p-4 bg-gym-primary/10 rounded-lg border border-gym-primary/20">
                                    <div class="flex items-center justify-between mb-2">
                                        <span class="text-gym-primary font-medium">Monthly Visits</span>
                                        <i class="fas fa-trophy text-gym-primary"></i>
                                    </div>
                                    <div class="w-full bg-gray-800 rounded-full h-2 mb-2">
                                        <div class="bg-gym-primary h-2 rounded-full" style="width: 90%"></div>
                                    </div>
                                    <p class="text-sm text-gray-400">18/20 visits</p>
                                </div>

                                <div class="p-4 bg-gym-secondary/10 rounded-lg border border-gym-secondary/20">
                                    <div class="flex items-center justify-between mb-2">
                                        <span class="text-gym-secondary font-medium">Workout Hours</span>
                                        <i class="fas fa-clock text-gym-secondary"></i>
                                    </div>
                                    <div class="w-full bg-gray-800 rounded-full h-2 mb-2">
                                        <div class="bg-gym-secondary h-2 rounded-full" style="width: 78%"></div>
                                    </div>
                                    <p class="text-sm text-gray-400">47/60 hours</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Member Registration Section -->
            <div id="registration-section" class="section hidden">
                <div class="max-w-4xl mx-auto">
                    <div class="bg-gym-dark rounded-xl p-8 border border-gray-800">
                        <div class="mb-8">
                            <h2 class="text-2xl font-bold text-white mb-2">New Member Registration</h2>
                            <p class="text-gray-400">Fill out the form below to register a new gym member</p>
                        </div>

                        <form class="space-y-8">
                            <!-- Personal Information -->
                            <div>
                                <h3 class="text-lg font-semibold text-white mb-4 flex items-center">
                                    <i class="fas fa-user mr-2 text-gym-primary"></i>
                                    Personal Information
                                </h3>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-300 mb-2">First Name *</label>
                                        <input type="text" required
                                               class="w-full bg-gray-800 border border-gray-700 rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-gym-primary focus:border-transparent"
                                               placeholder="Enter first name">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-300 mb-2">Last Name *</label>
                                        <input type="text" required
                                               class="w-full bg-gray-800 border border-gray-700 rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-gym-primary focus:border-transparent"
                                               placeholder="Enter last name">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-300 mb-2">Email Address *</label>
                                        <input type="email" required
                                               class="w-full bg-gray-800 border border-gray-700 rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-gym-primary focus:border-transparent"
                                               placeholder="Enter email address">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-300 mb-2">Phone Number *</label>
                                        <input type="tel" required
                                               class="w-full bg-gray-800 border border-gray-700 rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-gym-primary focus:border-transparent"
                                               placeholder="(*************">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-300 mb-2">Date of Birth *</label>
                                        <input type="date" required
                                               class="w-full bg-gray-800 border border-gray-700 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-gym-primary focus:border-transparent">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-300 mb-2">Gender</label>
                                        <select class="w-full bg-gray-800 border border-gray-700 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-gym-primary focus:border-transparent">
                                            <option value="">Select gender</option>
                                            <option value="male">Male</option>
                                            <option value="female">Female</option>
                                            <option value="other">Other</option>
                                            <option value="prefer-not-to-say">Prefer not to say</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <!-- Address Information -->
                            <div>
                                <h3 class="text-lg font-semibold text-white mb-4 flex items-center">
                                    <i class="fas fa-map-marker-alt mr-2 text-gym-secondary"></i>
                                    Address Information
                                </h3>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div class="md:col-span-2">
                                        <label class="block text-sm font-medium text-gray-300 mb-2">Street Address</label>
                                        <input type="text"
                                               class="w-full bg-gray-800 border border-gray-700 rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-gym-primary focus:border-transparent"
                                               placeholder="Enter street address">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-300 mb-2">City</label>
                                        <input type="text"
                                               class="w-full bg-gray-800 border border-gray-700 rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-gym-primary focus:border-transparent"
                                               placeholder="Enter city">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-300 mb-2">State</label>
                                        <input type="text"
                                               class="w-full bg-gray-800 border border-gray-700 rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-gym-primary focus:border-transparent"
                                               placeholder="Enter state">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-300 mb-2">ZIP Code</label>
                                        <input type="text"
                                               class="w-full bg-gray-800 border border-gray-700 rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-gym-primary focus:border-transparent"
                                               placeholder="Enter ZIP code">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-300 mb-2">Country</label>
                                        <select class="w-full bg-gray-800 border border-gray-700 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-gym-primary focus:border-transparent">
                                            <option value="us">United States</option>
                                            <option value="ca">Canada</option>
                                            <option value="uk">United Kingdom</option>
                                            <option value="other">Other</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <!-- Emergency Contact -->
                            <div>
                                <h3 class="text-lg font-semibold text-white mb-4 flex items-center">
                                    <i class="fas fa-phone mr-2 text-red-400"></i>
                                    Emergency Contact
                                </h3>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-300 mb-2">Contact Name</label>
                                        <input type="text"
                                               class="w-full bg-gray-800 border border-gray-700 rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-gym-primary focus:border-transparent"
                                               placeholder="Enter contact name">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-300 mb-2">Contact Phone</label>
                                        <input type="tel"
                                               class="w-full bg-gray-800 border border-gray-700 rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-gym-primary focus:border-transparent"
                                               placeholder="(*************">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-300 mb-2">Relationship</label>
                                        <select class="w-full bg-gray-800 border border-gray-700 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-gym-primary focus:border-transparent">
                                            <option value="">Select relationship</option>
                                            <option value="spouse">Spouse</option>
                                            <option value="parent">Parent</option>
                                            <option value="sibling">Sibling</option>
                                            <option value="friend">Friend</option>
                                            <option value="other">Other</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <!-- Membership Plan Selection -->
                            <div>
                                <h3 class="text-lg font-semibold text-white mb-4 flex items-center">
                                    <i class="fas fa-credit-card mr-2 text-gym-accent"></i>
                                    Membership Plan
                                </h3>
                                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                                    <div class="relative">
                                        <input type="radio" id="basic" name="membership" value="basic" class="sr-only peer">
                                        <label for="basic" class="block p-6 bg-gray-800 border-2 border-gray-700 rounded-xl cursor-pointer hover:border-gym-primary peer-checked:border-gym-primary peer-checked:bg-gym-primary/10 transition-all">
                                            <div class="text-center">
                                                <h4 class="text-lg font-semibold text-white mb-2">Basic</h4>
                                                <div class="text-3xl font-bold text-gym-primary mb-2">$29<span class="text-sm text-gray-400">/mo</span></div>
                                                <ul class="text-sm text-gray-300 space-y-2">
                                                    <li>• Gym access</li>
                                                    <li>• Basic equipment</li>
                                                    <li>• Locker room</li>
                                                </ul>
                                            </div>
                                        </label>
                                    </div>

                                    <div class="relative">
                                        <input type="radio" id="premium" name="membership" value="premium" class="sr-only peer">
                                        <label for="premium" class="block p-6 bg-gray-800 border-2 border-gray-700 rounded-xl cursor-pointer hover:border-gym-primary peer-checked:border-gym-primary peer-checked:bg-gym-primary/10 transition-all">
                                            <div class="text-center">
                                                <h4 class="text-lg font-semibold text-white mb-2">Premium</h4>
                                                <div class="text-3xl font-bold text-gym-primary mb-2">$59<span class="text-sm text-gray-400">/mo</span></div>
                                                <ul class="text-sm text-gray-300 space-y-2">
                                                    <li>• All Basic features</li>
                                                    <li>• Group classes</li>
                                                    <li>• Personal trainer</li>
                                                    <li>• Nutrition guidance</li>
                                                </ul>
                                            </div>
                                        </label>
                                    </div>

                                    <div class="relative">
                                        <input type="radio" id="elite" name="membership" value="elite" class="sr-only peer">
                                        <label for="elite" class="block p-6 bg-gray-800 border-2 border-gray-700 rounded-xl cursor-pointer hover:border-gym-primary peer-checked:border-gym-primary peer-checked:bg-gym-primary/10 transition-all">
                                            <div class="text-center">
                                                <h4 class="text-lg font-semibold text-white mb-2">Elite</h4>
                                                <div class="text-3xl font-bold text-gym-primary mb-2">$99<span class="text-sm text-gray-400">/mo</span></div>
                                                <ul class="text-sm text-gray-300 space-y-2">
                                                    <li>• All Premium features</li>
                                                    <li>• 24/7 access</li>
                                                    <li>• Guest privileges</li>
                                                    <li>• Spa access</li>
                                                </ul>
                                            </div>
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <!-- Health Information -->
                            <div>
                                <h3 class="text-lg font-semibold text-white mb-4 flex items-center">
                                    <i class="fas fa-heartbeat mr-2 text-red-400"></i>
                                    Health Information
                                </h3>
                                <div class="space-y-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-300 mb-2">Medical Conditions</label>
                                        <textarea rows="3"
                                                  class="w-full bg-gray-800 border border-gray-700 rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-gym-primary focus:border-transparent"
                                                  placeholder="List any medical conditions or concerns..."></textarea>
                                    </div>
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                        <div>
                                            <label class="block text-sm font-medium text-gray-300 mb-2">Fitness Goals</label>
                                            <select multiple class="w-full bg-gray-800 border border-gray-700 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-gym-primary focus:border-transparent h-32">
                                                <option value="weight-loss">Weight Loss</option>
                                                <option value="muscle-gain">Muscle Gain</option>
                                                <option value="endurance">Endurance</option>
                                                <option value="strength">Strength Training</option>
                                                <option value="flexibility">Flexibility</option>
                                                <option value="general-fitness">General Fitness</option>
                                            </select>
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-300 mb-2">Experience Level</label>
                                            <select class="w-full bg-gray-800 border border-gray-700 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-gym-primary focus:border-transparent">
                                                <option value="">Select experience level</option>
                                                <option value="beginner">Beginner</option>
                                                <option value="intermediate">Intermediate</option>
                                                <option value="advanced">Advanced</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Terms and Conditions -->
                            <div>
                                <div class="bg-gray-800/50 rounded-lg p-6">
                                    <div class="space-y-4">
                                        <div class="flex items-start space-x-3">
                                            <input type="checkbox" id="terms" required
                                                   class="mt-1 w-4 h-4 text-gym-primary bg-gray-800 border-gray-600 rounded focus:ring-gym-primary focus:ring-2">
                                            <label for="terms" class="text-sm text-gray-300">
                                                I agree to the <a href="#" class="text-gym-primary hover:underline">Terms and Conditions</a> and <a href="#" class="text-gym-primary hover:underline">Privacy Policy</a>
                                            </label>
                                        </div>
                                        <div class="flex items-start space-x-3">
                                            <input type="checkbox" id="waiver" required
                                                   class="mt-1 w-4 h-4 text-gym-primary bg-gray-800 border-gray-600 rounded focus:ring-gym-primary focus:ring-2">
                                            <label for="waiver" class="text-sm text-gray-300">
                                                I acknowledge that I have read and understood the liability waiver
                                            </label>
                                        </div>
                                        <div class="flex items-start space-x-3">
                                            <input type="checkbox" id="marketing"
                                                   class="mt-1 w-4 h-4 text-gym-primary bg-gray-800 border-gray-600 rounded focus:ring-gym-primary focus:ring-2">
                                            <label for="marketing" class="text-sm text-gray-300">
                                                I would like to receive promotional emails and updates
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Form Actions -->
                            <div class="flex flex-col sm:flex-row gap-4 pt-6 border-t border-gray-800">
                                <button type="button" class="flex-1 bg-gym-blue/20 hover:bg-gym-blue/30 text-gym-blue border border-gym-blue/30 py-3 px-6 rounded-lg transition-colors">
                                    Save as Draft
                                </button>
                                <button type="submit" class="flex-1 bg-gym-primary hover:bg-gym-primary/80 text-white py-3 px-6 rounded-lg transition-colors font-medium">
                                    Register Member
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Members Directory Section -->
            <div id="members-section" class="section hidden">
                <!-- Search and Filters -->
                <div class="bg-gym-dark rounded-xl p-6 border border-gray-800 mb-6">
                    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                        <div class="flex-1 max-w-md">
                            <div class="relative">
                                <input type="text" placeholder="Search members..."
                                       class="w-full bg-gray-800 border border-gray-700 rounded-lg px-4 py-3 pl-10 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-gym-primary focus:border-transparent">
                                <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                            </div>
                        </div>

                        <div class="flex flex-wrap gap-3">
                            <select class="bg-gray-800 border border-gray-700 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-gym-primary focus:border-transparent">
                                <option value="">All Status</option>
                                <option value="active">Active</option>
                                <option value="inactive">Inactive</option>
                                <option value="suspended">Suspended</option>
                            </select>

                            <select class="bg-gray-800 border border-gray-700 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-gym-primary focus:border-transparent">
                                <option value="">All Plans</option>
                                <option value="basic">Basic</option>
                                <option value="premium">Premium</option>
                                <option value="elite">Elite</option>
                            </select>

                            <button class="bg-gym-primary hover:bg-gym-primary/80 text-white px-4 py-3 rounded-lg transition-colors">
                                <i class="fas fa-filter mr-2"></i>Filter
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Members Grid -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-6">
                    <!-- Member Card 1 -->
                    <div class="bg-gym-dark rounded-xl p-6 border border-gray-800 hover:border-gym-primary/50 transition-colors">
                        <div class="flex items-center justify-between mb-4">
                            <div class="w-12 h-12 bg-gym-primary rounded-full flex items-center justify-center">
                                <i class="fas fa-user text-white"></i>
                            </div>
                            <span class="bg-gym-primary text-white text-xs px-2 py-1 rounded-full">Active</span>
                        </div>
                        <h3 class="text-lg font-semibold text-white mb-1">John Smith</h3>
                        <p class="text-gray-400 text-sm mb-3">Premium Member</p>
                        <div class="space-y-2 text-sm">
                            <div class="flex items-center justify-between">
                                <span class="text-gray-400">ID:</span>
                                <span class="text-white">#12345</span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-gray-400">Joined:</span>
                                <span class="text-white">Jan 2024</span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-gray-400">Last Visit:</span>
                                <span class="text-gym-primary">Today</span>
                            </div>
                        </div>
                        <div class="mt-4 pt-4 border-t border-gray-800 flex gap-2">
                            <button class="flex-1 bg-gym-primary/20 hover:bg-gym-primary/30 text-gym-primary py-2 px-3 rounded-lg text-sm transition-colors">
                                View
                            </button>
                            <button class="flex-1 bg-gray-700 hover:bg-gray-600 text-white py-2 px-3 rounded-lg text-sm transition-colors">
                                Edit
                            </button>
                        </div>
                    </div>

                    <!-- Member Card 2 -->
                    <div class="bg-gym-dark rounded-xl p-6 border border-gray-800 hover:border-gym-primary/50 transition-colors">
                        <div class="flex items-center justify-between mb-4">
                            <div class="w-12 h-12 bg-gym-secondary rounded-full flex items-center justify-center">
                                <i class="fas fa-user text-white"></i>
                            </div>
                            <span class="bg-gym-secondary text-white text-xs px-2 py-1 rounded-full">Active</span>
                        </div>
                        <h3 class="text-lg font-semibold text-white mb-1">Sarah Johnson</h3>
                        <p class="text-gray-400 text-sm mb-3">Basic Member</p>
                        <div class="space-y-2 text-sm">
                            <div class="flex items-center justify-between">
                                <span class="text-gray-400">ID:</span>
                                <span class="text-white">#12346</span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-gray-400">Joined:</span>
                                <span class="text-white">Feb 2024</span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-gray-400">Last Visit:</span>
                                <span class="text-white">Yesterday</span>
                            </div>
                        </div>
                        <div class="mt-4 pt-4 border-t border-gray-800 flex gap-2">
                            <button class="flex-1 bg-gym-primary/20 hover:bg-gym-primary/30 text-gym-primary py-2 px-3 rounded-lg text-sm transition-colors">
                                View
                            </button>
                            <button class="flex-1 bg-gray-700 hover:bg-gray-600 text-white py-2 px-3 rounded-lg text-sm transition-colors">
                                Edit
                            </button>
                        </div>
                    </div>

                    <!-- Member Card 3 -->
                    <div class="bg-gym-dark rounded-xl p-6 border border-gray-800 hover:border-gym-blue/50 transition-colors">
                        <div class="flex items-center justify-between mb-4">
                            <div class="w-12 h-12 bg-gym-blue rounded-full flex items-center justify-center">
                                <i class="fas fa-user text-white"></i>
                            </div>
                            <span class="bg-gym-blue text-white text-xs px-2 py-1 rounded-full">Active</span>
                        </div>
                        <h3 class="text-lg font-semibold text-white mb-1">Mike Davis</h3>
                        <p class="text-gray-400 text-sm mb-3">Elite Member</p>
                        <div class="space-y-2 text-sm">
                            <div class="flex items-center justify-between">
                                <span class="text-gray-400">ID:</span>
                                <span class="text-white">#12347</span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-gray-400">Joined:</span>
                                <span class="text-white">Dec 2023</span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-gray-400">Last Visit:</span>
                                <span class="text-white">2 days ago</span>
                            </div>
                        </div>
                        <div class="mt-4 pt-4 border-t border-gray-800 flex gap-2">
                            <button class="flex-1 bg-gym-blue/20 hover:bg-gym-blue/30 text-gym-blue py-2 px-3 rounded-lg text-sm transition-colors">
                                View
                            </button>
                            <button class="flex-1 bg-gray-700 hover:bg-gray-600 text-white py-2 px-3 rounded-lg text-sm transition-colors">
                                Edit
                            </button>
                        </div>
                    </div>

                    <!-- Member Card 4 -->
                    <div class="bg-gym-dark rounded-xl p-6 border border-gray-800 hover:border-gym-primary/50 transition-colors">
                        <div class="flex items-center justify-between mb-4">
                            <div class="w-12 h-12 bg-gray-600 rounded-full flex items-center justify-center">
                                <i class="fas fa-user text-white"></i>
                            </div>
                            <span class="bg-gray-600 text-white text-xs px-2 py-1 rounded-full">Inactive</span>
                        </div>
                        <h3 class="text-lg font-semibold text-white mb-1">Lisa Wilson</h3>
                        <p class="text-gray-400 text-sm mb-3">Premium Member</p>
                        <div class="space-y-2 text-sm">
                            <div class="flex items-center justify-between">
                                <span class="text-gray-400">ID:</span>
                                <span class="text-white">#12348</span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-gray-400">Joined:</span>
                                <span class="text-white">Nov 2023</span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-gray-400">Last Visit:</span>
                                <span class="text-gray-500">2 weeks ago</span>
                            </div>
                        </div>
                        <div class="mt-4 pt-4 border-t border-gray-800 flex gap-2">
                            <button class="flex-1 bg-gym-primary/20 hover:bg-gym-primary/30 text-gym-primary py-2 px-3 rounded-lg text-sm transition-colors">
                                View
                            </button>
                            <button class="flex-1 bg-gray-700 hover:bg-gray-600 text-white py-2 px-3 rounded-lg text-sm transition-colors">
                                Edit
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Pagination -->
                <div class="bg-gym-dark rounded-xl p-6 border border-gray-800">
                    <div class="flex items-center justify-between">
                        <div class="text-sm text-gray-400">
                            Showing 1-12 of 1,247 members
                        </div>
                        <div class="flex items-center space-x-2">
                            <button class="px-3 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors disabled:opacity-50" disabled>
                                <i class="fas fa-chevron-left"></i>
                            </button>
                            <button class="px-3 py-2 bg-gym-primary text-white rounded-lg">1</button>
                            <button class="px-3 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors">2</button>
                            <button class="px-3 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors">3</button>
                            <span class="px-3 py-2 text-gray-400">...</span>
                            <button class="px-3 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors">104</button>
                            <button class="px-3 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors">
                                <i class="fas fa-chevron-right"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Membership Plans Section -->
            <div id="memberships-section" class="section hidden">
                <div class="mb-8">
                    <h2 class="text-2xl font-bold text-white mb-2">Membership Plans</h2>
                    <p class="text-gray-400">Choose the perfect plan for your fitness journey</p>
                </div>

                <!-- Pricing Cards -->
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
                    <!-- Basic Plan -->
                    <div class="bg-gym-dark rounded-2xl p-8 border border-gray-800 hover:border-gym-primary/50 transition-all duration-300">
                        <div class="text-center mb-8">
                            <div class="w-16 h-16 bg-gym-secondary/20 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-dumbbell text-gym-secondary text-2xl"></i>
                            </div>
                            <h3 class="text-2xl font-bold text-white mb-2">Basic</h3>
                            <p class="text-gray-400 mb-6">Perfect for getting started</p>
                            <div class="mb-6">
                                <span class="text-5xl font-bold text-white">$29</span>
                                <span class="text-gray-400">/month</span>
                            </div>
                        </div>

                        <ul class="space-y-4 mb-8">
                            <li class="flex items-center text-gray-300">
                                <i class="fas fa-check text-gym-secondary mr-3"></i>
                                Access to gym equipment
                            </li>
                            <li class="flex items-center text-gray-300">
                                <i class="fas fa-check text-gym-secondary mr-3"></i>
                                Locker room access
                            </li>
                            <li class="flex items-center text-gray-300">
                                <i class="fas fa-check text-gym-secondary mr-3"></i>
                                Basic workout tracking
                            </li>
                            <li class="flex items-center text-gray-300">
                                <i class="fas fa-check text-gym-secondary mr-3"></i>
                                Mobile app access
                            </li>
                            <li class="flex items-center text-gray-500">
                                <i class="fas fa-times text-gray-600 mr-3"></i>
                                Group fitness classes
                            </li>
                            <li class="flex items-center text-gray-500">
                                <i class="fas fa-times text-gray-600 mr-3"></i>
                                Personal training
                            </li>
                        </ul>

                        <button class="w-full bg-gym-secondary hover:bg-gym-secondary/80 text-white py-3 px-6 rounded-lg font-medium transition-colors">
                            Choose Basic
                        </button>
                    </div>

                    <!-- Premium Plan -->
                    <div class="bg-gym-dark rounded-2xl p-8 border-2 border-gym-primary relative hover:border-gym-primary/80 transition-all duration-300 transform hover:scale-105">
                        <div class="absolute -top-4 left-1/2 transform -translate-x-1/2">
                            <span class="bg-gym-primary text-white px-4 py-2 rounded-full text-sm font-medium">Most Popular</span>
                        </div>

                        <div class="text-center mb-8">
                            <div class="w-16 h-16 bg-gym-primary/20 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-fire text-gym-primary text-2xl"></i>
                            </div>
                            <h3 class="text-2xl font-bold text-white mb-2">Premium</h3>
                            <p class="text-gray-400 mb-6">For serious fitness enthusiasts</p>
                            <div class="mb-6">
                                <span class="text-5xl font-bold text-white">$59</span>
                                <span class="text-gray-400">/month</span>
                            </div>
                        </div>

                        <ul class="space-y-4 mb-8">
                            <li class="flex items-center text-gray-300">
                                <i class="fas fa-check text-gym-primary mr-3"></i>
                                Everything in Basic
                            </li>
                            <li class="flex items-center text-gray-300">
                                <i class="fas fa-check text-gym-primary mr-3"></i>
                                Unlimited group classes
                            </li>
                            <li class="flex items-center text-gray-300">
                                <i class="fas fa-check text-gym-primary mr-3"></i>
                                2 personal training sessions
                            </li>
                            <li class="flex items-center text-gray-300">
                                <i class="fas fa-check text-gym-primary mr-3"></i>
                                Nutrition consultation
                            </li>
                            <li class="flex items-center text-gray-300">
                                <i class="fas fa-check text-gym-primary mr-3"></i>
                                Priority booking
                            </li>
                            <li class="flex items-center text-gray-500">
                                <i class="fas fa-times text-gray-600 mr-3"></i>
                                24/7 gym access
                            </li>
                        </ul>

                        <button class="w-full bg-gym-primary hover:bg-gym-primary/80 text-white py-3 px-6 rounded-lg font-medium transition-colors">
                            Choose Premium
                        </button>
                    </div>

                    <!-- Elite Plan -->
                    <div class="bg-gym-dark rounded-2xl p-8 border border-gray-800 hover:border-gym-blue/50 transition-all duration-300">
                        <div class="text-center mb-8">
                            <div class="w-16 h-16 bg-gym-blue/20 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-crown text-gym-blue text-2xl"></i>
                            </div>
                            <h3 class="text-2xl font-bold text-white mb-2">Elite</h3>
                            <p class="text-gray-400 mb-6">Ultimate fitness experience</p>
                            <div class="mb-6">
                                <span class="text-5xl font-bold text-white">$99</span>
                                <span class="text-gray-400">/month</span>
                            </div>
                        </div>

                        <ul class="space-y-4 mb-8">
                            <li class="flex items-center text-gray-300">
                                <i class="fas fa-check text-gym-blue mr-3"></i>
                                Everything in Premium
                            </li>
                            <li class="flex items-center text-gray-300">
                                <i class="fas fa-check text-gym-blue mr-3"></i>
                                24/7 gym access
                            </li>
                            <li class="flex items-center text-gray-300">
                                <i class="fas fa-check text-gym-blue mr-3"></i>
                                Unlimited personal training
                            </li>
                            <li class="flex items-center text-gray-300">
                                <i class="fas fa-check text-gym-blue mr-3"></i>
                                Guest privileges (2 per month)
                            </li>
                            <li class="flex items-center text-gray-300">
                                <i class="fas fa-check text-gym-blue mr-3"></i>
                                Spa & sauna access
                            </li>
                            <li class="flex items-center text-gray-300">
                                <i class="fas fa-check text-gym-blue mr-3"></i>
                                Exclusive member events
                            </li>
                        </ul>

                        <button class="w-full bg-gym-blue hover:bg-gym-blue/80 text-white py-3 px-6 rounded-lg font-medium transition-colors">
                            Choose Elite
                        </button>
                    </div>
                </div>

                <!-- Plan Comparison Table -->
                <div class="bg-gym-dark rounded-xl border border-gray-800 overflow-hidden">
                    <div class="p-6 border-b border-gray-800">
                        <h3 class="text-xl font-bold text-white">Plan Comparison</h3>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead class="bg-gray-800/50">
                                <tr>
                                    <th class="text-left p-4 text-gray-300 font-medium">Features</th>
                                    <th class="text-center p-4 text-gray-300 font-medium">Basic</th>
                                    <th class="text-center p-4 text-gym-primary font-medium">Premium</th>
                                    <th class="text-center p-4 text-gym-blue font-medium">Elite</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-800">
                                <tr>
                                    <td class="p-4 text-gray-300">Gym Equipment Access</td>
                                    <td class="p-4 text-center"><i class="fas fa-check text-gym-secondary"></i></td>
                                    <td class="p-4 text-center"><i class="fas fa-check text-gym-primary"></i></td>
                                    <td class="p-4 text-center"><i class="fas fa-check text-gym-blue"></i></td>
                                </tr>
                                <tr class="bg-gray-800/25">
                                    <td class="p-4 text-gray-300">Group Fitness Classes</td>
                                    <td class="p-4 text-center"><i class="fas fa-times text-gray-600"></i></td>
                                    <td class="p-4 text-center"><i class="fas fa-check text-gym-primary"></i></td>
                                    <td class="p-4 text-center"><i class="fas fa-check text-gym-blue"></i></td>
                                </tr>
                                <tr>
                                    <td class="p-4 text-gray-300">Personal Training Sessions</td>
                                    <td class="p-4 text-center text-gray-600">0</td>
                                    <td class="p-4 text-center text-gym-primary">2/month</td>
                                    <td class="p-4 text-center text-gym-blue">Unlimited</td>
                                </tr>
                                <tr class="bg-gray-800/25">
                                    <td class="p-4 text-gray-300">24/7 Access</td>
                                    <td class="p-4 text-center"><i class="fas fa-times text-gray-600"></i></td>
                                    <td class="p-4 text-center"><i class="fas fa-times text-gray-600"></i></td>
                                    <td class="p-4 text-center"><i class="fas fa-check text-gym-blue"></i></td>
                                </tr>
                                <tr>
                                    <td class="p-4 text-gray-300">Guest Privileges</td>
                                    <td class="p-4 text-center text-gray-600">0</td>
                                    <td class="p-4 text-center text-gray-600">0</td>
                                    <td class="p-4 text-center text-gym-blue">2/month</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Check-in/Check-out Section -->
            <div id="checkin-section" class="section hidden">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <!-- Quick Check-in -->
                    <div class="bg-gym-dark rounded-xl p-6 border border-gray-800">
                        <h3 class="text-xl font-bold text-white mb-6 flex items-center">
                            <i class="fas fa-sign-in-alt mr-3 text-gym-primary"></i>
                            Quick Check-in
                        </h3>

                        <div class="space-y-4">
                            <div class="relative">
                                <input type="text" placeholder="Enter Member ID or scan card..."
                                       class="w-full bg-gray-800 border border-gray-700 rounded-lg px-4 py-4 pl-12 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-gym-primary focus:border-transparent text-lg">
                                <i class="fas fa-id-card absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                            </div>

                            <div class="grid grid-cols-2 gap-4">
                                <button class="bg-gym-primary hover:bg-gym-primary/80 text-white py-3 px-4 rounded-lg font-medium transition-colors">
                                    <i class="fas fa-sign-in-alt mr-2"></i>Check In
                                </button>
                                <button class="bg-gym-secondary hover:bg-gym-secondary/80 text-white py-3 px-4 rounded-lg font-medium transition-colors">
                                    <i class="fas fa-sign-out-alt mr-2"></i>Check Out
                                </button>
                            </div>
                        </div>

                        <!-- Member Info Display -->
                        <div id="member-info" class="mt-6 p-4 bg-gray-800/50 rounded-lg hidden">
                            <div class="flex items-center space-x-4">
                                <div class="w-12 h-12 bg-gym-primary rounded-full flex items-center justify-center">
                                    <i class="fas fa-user text-white"></i>
                                </div>
                                <div>
                                    <h4 class="text-white font-medium">John Smith</h4>
                                    <p class="text-gray-400 text-sm">Premium Member • ID: #12345</p>
                                </div>
                                <div class="ml-auto">
                                    <span class="bg-gym-primary text-white text-xs px-2 py-1 rounded-full">Active</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Current Status -->
                    <div class="bg-gym-dark rounded-xl p-6 border border-gray-800">
                        <h3 class="text-xl font-bold text-white mb-6 flex items-center">
                            <i class="fas fa-users mr-3 text-gym-secondary"></i>
                            Current Status
                        </h3>

                        <div class="grid grid-cols-2 gap-4 mb-6">
                            <div class="bg-gym-primary/10 rounded-lg p-4 border border-gym-primary/20">
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-gym-primary">89</div>
                                    <div class="text-sm text-gray-400">Currently In</div>
                                </div>
                            </div>
                            <div class="bg-gym-secondary/10 rounded-lg p-4 border border-gym-secondary/20">
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-gym-secondary">156</div>
                                    <div class="text-sm text-gray-400">Today's Total</div>
                                </div>
                            </div>
                        </div>

                        <div class="space-y-3">
                            <div class="flex items-center justify-between text-sm">
                                <span class="text-gray-400">Peak Hours</span>
                                <span class="text-white">6-8 AM, 6-8 PM</span>
                            </div>
                            <div class="flex items-center justify-between text-sm">
                                <span class="text-gray-400">Average Stay</span>
                                <span class="text-white">1h 45m</span>
                            </div>
                            <div class="flex items-center justify-between text-sm">
                                <span class="text-gray-400">Capacity</span>
                                <span class="text-gym-primary">59% (89/150)</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Check-ins -->
                <div class="mt-8 bg-gym-dark rounded-xl p-6 border border-gray-800">
                    <div class="flex items-center justify-between mb-6">
                        <h3 class="text-xl font-bold text-white">Recent Check-ins</h3>
                        <button class="text-gym-primary hover:text-gym-primary/80 text-sm font-medium">View All</button>
                    </div>

                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead>
                                <tr class="border-b border-gray-800">
                                    <th class="text-left py-3 text-gray-400 font-medium">Member</th>
                                    <th class="text-left py-3 text-gray-400 font-medium">Plan</th>
                                    <th class="text-left py-3 text-gray-400 font-medium">Check-in Time</th>
                                    <th class="text-left py-3 text-gray-400 font-medium">Status</th>
                                    <th class="text-left py-3 text-gray-400 font-medium">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-800">
                                <tr>
                                    <td class="py-4">
                                        <div class="flex items-center space-x-3">
                                            <div class="w-8 h-8 bg-gym-primary rounded-full flex items-center justify-center">
                                                <i class="fas fa-user text-white text-xs"></i>
                                            </div>
                                            <div>
                                                <div class="text-white font-medium">John Smith</div>
                                                <div class="text-gray-400 text-sm">#12345</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="py-4 text-gray-300">Premium</td>
                                    <td class="py-4 text-gray-300">2 minutes ago</td>
                                    <td class="py-4">
                                        <span class="bg-gym-primary text-white text-xs px-2 py-1 rounded-full">Active</span>
                                    </td>
                                    <td class="py-4">
                                        <button class="text-gym-secondary hover:text-gym-secondary/80 text-sm">Check Out</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="py-4">
                                        <div class="flex items-center space-x-3">
                                            <div class="w-8 h-8 bg-gym-secondary rounded-full flex items-center justify-center">
                                                <i class="fas fa-user text-white text-xs"></i>
                                            </div>
                                            <div>
                                                <div class="text-white font-medium">Sarah Johnson</div>
                                                <div class="text-gray-400 text-sm">#12346</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="py-4 text-gray-300">Basic</td>
                                    <td class="py-4 text-gray-300">15 minutes ago</td>
                                    <td class="py-4">
                                        <span class="bg-gym-primary text-white text-xs px-2 py-1 rounded-full">Active</span>
                                    </td>
                                    <td class="py-4">
                                        <button class="text-gym-secondary hover:text-gym-secondary/80 text-sm">Check Out</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="py-4">
                                        <div class="flex items-center space-x-3">
                                            <div class="w-8 h-8 bg-gray-600 rounded-full flex items-center justify-center">
                                                <i class="fas fa-user text-white text-xs"></i>
                                            </div>
                                            <div>
                                                <div class="text-white font-medium">Mike Davis</div>
                                                <div class="text-gray-400 text-sm">#12347</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="py-4 text-gray-300">Elite</td>
                                    <td class="py-4 text-gray-300">1 hour ago</td>
                                    <td class="py-4">
                                        <span class="bg-gray-600 text-white text-xs px-2 py-1 rounded-full">Checked Out</span>
                                    </td>
                                    <td class="py-4">
                                        <span class="text-gray-500 text-sm">1h 30m visit</span>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Billing Section -->
            <div id="billing-section" class="section hidden">
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    <!-- Payment Overview -->
                    <div class="lg:col-span-2">
                        <div class="bg-gym-dark rounded-xl p-6 border border-gray-800 mb-6">
                            <h3 class="text-xl font-bold text-white mb-6">Payment History</h3>

                            <div class="overflow-x-auto">
                                <table class="w-full">
                                    <thead>
                                        <tr class="border-b border-gray-800">
                                            <th class="text-left py-3 text-gray-400 font-medium">Date</th>
                                            <th class="text-left py-3 text-gray-400 font-medium">Member</th>
                                            <th class="text-left py-3 text-gray-400 font-medium">Plan</th>
                                            <th class="text-left py-3 text-gray-400 font-medium">Amount</th>
                                            <th class="text-left py-3 text-gray-400 font-medium">Status</th>
                                            <th class="text-left py-3 text-gray-400 font-medium">Method</th>
                                        </tr>
                                    </thead>
                                    <tbody class="divide-y divide-gray-800">
                                        <tr>
                                            <td class="py-4 text-gray-300">Mar 12, 2024</td>
                                            <td class="py-4">
                                                <div class="text-white font-medium">John Smith</div>
                                                <div class="text-gray-400 text-sm">#12345</div>
                                            </td>
                                            <td class="py-4 text-gray-300">Premium</td>
                                            <td class="py-4 text-white font-medium">$59.99</td>
                                            <td class="py-4">
                                                <span class="bg-green-500 text-white text-xs px-2 py-1 rounded-full">Paid</span>
                                            </td>
                                            <td class="py-4 text-gray-300">Credit Card</td>
                                        </tr>
                                        <tr>
                                            <td class="py-4 text-gray-300">Mar 11, 2024</td>
                                            <td class="py-4">
                                                <div class="text-white font-medium">Sarah Johnson</div>
                                                <div class="text-gray-400 text-sm">#12346</div>
                                            </td>
                                            <td class="py-4 text-gray-300">Basic</td>
                                            <td class="py-4 text-white font-medium">$29.99</td>
                                            <td class="py-4">
                                                <span class="bg-green-500 text-white text-xs px-2 py-1 rounded-full">Paid</span>
                                            </td>
                                            <td class="py-4 text-gray-300">Bank Transfer</td>
                                        </tr>
                                        <tr>
                                            <td class="py-4 text-gray-300">Mar 10, 2024</td>
                                            <td class="py-4">
                                                <div class="text-white font-medium">Mike Davis</div>
                                                <div class="text-gray-400 text-sm">#12347</div>
                                            </td>
                                            <td class="py-4 text-gray-300">Elite</td>
                                            <td class="py-4 text-white font-medium">$99.99</td>
                                            <td class="py-4">
                                                <span class="bg-gym-secondary text-white text-xs px-2 py-1 rounded-full">Pending</span>
                                            </td>
                                            <td class="py-4 text-gray-300">Credit Card</td>
                                        </tr>
                                        <tr>
                                            <td class="py-4 text-gray-300">Mar 09, 2024</td>
                                            <td class="py-4">
                                                <div class="text-white font-medium">Lisa Wilson</div>
                                                <div class="text-gray-400 text-sm">#12348</div>
                                            </td>
                                            <td class="py-4 text-gray-300">Premium</td>
                                            <td class="py-4 text-white font-medium">$59.99</td>
                                            <td class="py-4">
                                                <span class="bg-red-500 text-white text-xs px-2 py-1 rounded-full">Failed</span>
                                            </td>
                                            <td class="py-4 text-gray-300">Credit Card</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- Outstanding Payments -->
                        <div class="bg-gym-dark rounded-xl p-6 border border-gray-800">
                            <h3 class="text-xl font-bold text-white mb-6">Outstanding Payments</h3>

                            <div class="space-y-4">
                                <div class="flex items-center justify-between p-4 bg-red-500/10 border border-red-500/20 rounded-lg">
                                    <div class="flex items-center space-x-4">
                                        <div class="w-10 h-10 bg-red-500 rounded-full flex items-center justify-center">
                                            <i class="fas fa-exclamation text-white"></i>
                                        </div>
                                        <div>
                                            <div class="text-white font-medium">Lisa Wilson - Premium</div>
                                            <div class="text-gray-400 text-sm">Payment failed • Due: Mar 09, 2024</div>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <div class="text-white font-bold">$59.99</div>
                                        <button class="text-gym-primary hover:text-gym-primary/80 text-sm">Retry Payment</button>
                                    </div>
                                </div>

                                <div class="flex items-center justify-between p-4 bg-gym-secondary/10 border border-gym-secondary/20 rounded-lg">
                                    <div class="flex items-center space-x-4">
                                        <div class="w-10 h-10 bg-gym-secondary rounded-full flex items-center justify-center">
                                            <i class="fas fa-clock text-white"></i>
                                        </div>
                                        <div>
                                            <div class="text-white font-medium">Mike Davis - Elite</div>
                                            <div class="text-gray-400 text-sm">Payment processing • Due: Mar 10, 2024</div>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <div class="text-white font-bold">$99.99</div>
                                        <span class="text-gym-secondary text-sm">Processing</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Revenue Summary -->
                    <div class="lg:col-span-1">
                        <div class="bg-gym-dark rounded-xl p-6 border border-gray-800 mb-6">
                            <h3 class="text-lg font-semibold text-white mb-4">Revenue Summary</h3>

                            <div class="space-y-4">
                                <div class="p-4 bg-gym-primary/10 rounded-lg border border-gym-primary/20">
                                    <div class="text-center">
                                        <div class="text-2xl font-bold text-gym-primary">$24,580</div>
                                        <div class="text-sm text-gray-400">This Month</div>
                                    </div>
                                </div>

                                <div class="space-y-3">
                                    <div class="flex items-center justify-between">
                                        <span class="text-gray-400">Basic Plans</span>
                                        <span class="text-white">$8,970</span>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <span class="text-gray-400">Premium Plans</span>
                                        <span class="text-white">$11,980</span>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <span class="text-gray-400">Elite Plans</span>
                                        <span class="text-white">$3,630</span>
                                    </div>
                                </div>

                                <div class="pt-4 border-t border-gray-800">
                                    <div class="flex items-center justify-between">
                                        <span class="text-gray-400">Outstanding</span>
                                        <span class="text-red-400">$159.98</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Payment Methods -->
                        <div class="bg-gym-dark rounded-xl p-6 border border-gray-800">
                            <h3 class="text-lg font-semibold text-white mb-4">Payment Methods</h3>

                            <div class="space-y-3">
                                <div class="flex items-center justify-between p-3 bg-gray-800/50 rounded-lg">
                                    <div class="flex items-center space-x-3">
                                        <i class="fas fa-credit-card text-gym-primary"></i>
                                        <span class="text-white">Credit Cards</span>
                                    </div>
                                    <span class="text-gray-400">78%</span>
                                </div>

                                <div class="flex items-center justify-between p-3 bg-gray-800/50 rounded-lg">
                                    <div class="flex items-center space-x-3">
                                        <i class="fas fa-university text-gym-secondary"></i>
                                        <span class="text-white">Bank Transfer</span>
                                    </div>
                                    <span class="text-gray-400">15%</span>
                                </div>

                                <div class="flex items-center justify-between p-3 bg-gray-800/50 rounded-lg">
                                    <div class="flex items-center space-x-3">
                                        <i class="fas fa-money-bill text-gym-accent"></i>
                                        <span class="text-white">Cash</span>
                                    </div>
                                    <span class="text-gray-400">7%</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Class Schedule Section -->
            <div id="schedule-section" class="section hidden">
                <div class="mb-6">
                    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                        <div>
                            <h2 class="text-2xl font-bold text-white mb-2">Class Schedule</h2>
                            <p class="text-gray-400">Manage fitness classes and bookings</p>
                        </div>
                        <div class="flex gap-3">
                            <button class="bg-gym-primary hover:bg-gym-primary/80 text-white px-4 py-2 rounded-lg transition-colors">
                                <i class="fas fa-plus mr-2"></i>Add Class
                            </button>
                            <select class="bg-gray-800 border border-gray-700 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-gym-primary">
                                <option>This Week</option>
                                <option>Next Week</option>
                                <option>This Month</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Weekly Schedule -->
                <div class="bg-gym-dark rounded-xl border border-gray-800 overflow-hidden">
                    <div class="overflow-x-auto">
                        <table class="w-full min-w-[800px]">
                            <thead class="bg-gray-800/50">
                                <tr>
                                    <th class="text-left p-4 text-gray-300 font-medium w-24">Time</th>
                                    <th class="text-center p-4 text-gray-300 font-medium">Monday</th>
                                    <th class="text-center p-4 text-gray-300 font-medium">Tuesday</th>
                                    <th class="text-center p-4 text-gray-300 font-medium">Wednesday</th>
                                    <th class="text-center p-4 text-gray-300 font-medium">Thursday</th>
                                    <th class="text-center p-4 text-gray-300 font-medium">Friday</th>
                                    <th class="text-center p-4 text-gray-300 font-medium">Saturday</th>
                                    <th class="text-center p-4 text-gray-300 font-medium">Sunday</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-800">
                                <tr>
                                    <td class="p-4 text-gray-400 font-medium">6:00 AM</td>
                                    <td class="p-2">
                                        <div class="bg-gym-primary/20 border border-gym-primary/30 rounded-lg p-3 text-center">
                                            <div class="text-gym-primary font-medium text-sm">Morning Yoga</div>
                                            <div class="text-gray-400 text-xs">Sarah K.</div>
                                            <div class="text-gray-400 text-xs">12/15</div>
                                        </div>
                                    </td>
                                    <td class="p-2"></td>
                                    <td class="p-2">
                                        <div class="bg-gym-primary/20 border border-gym-primary/30 rounded-lg p-3 text-center">
                                            <div class="text-gym-primary font-medium text-sm">Morning Yoga</div>
                                            <div class="text-gray-400 text-xs">Sarah K.</div>
                                            <div class="text-gray-400 text-xs">10/15</div>
                                        </div>
                                    </td>
                                    <td class="p-2"></td>
                                    <td class="p-2">
                                        <div class="bg-gym-primary/20 border border-gym-primary/30 rounded-lg p-3 text-center">
                                            <div class="text-gym-primary font-medium text-sm">Morning Yoga</div>
                                            <div class="text-gray-400 text-xs">Sarah K.</div>
                                            <div class="text-gray-400 text-xs">8/15</div>
                                        </div>
                                    </td>
                                    <td class="p-2"></td>
                                    <td class="p-2"></td>
                                </tr>
                                <tr>
                                    <td class="p-4 text-gray-400 font-medium">7:00 AM</td>
                                    <td class="p-2">
                                        <div class="bg-gym-secondary/20 border border-gym-secondary/30 rounded-lg p-3 text-center">
                                            <div class="text-gym-secondary font-medium text-sm">HIIT Training</div>
                                            <div class="text-gray-400 text-xs">Mike R.</div>
                                            <div class="text-gray-400 text-xs">20/20</div>
                                        </div>
                                    </td>
                                    <td class="p-2">
                                        <div class="bg-gym-accent/20 border border-gym-accent/30 rounded-lg p-3 text-center">
                                            <div class="text-gym-accent font-medium text-sm">Pilates</div>
                                            <div class="text-gray-400 text-xs">Emma L.</div>
                                            <div class="text-gray-400 text-xs">14/18</div>
                                        </div>
                                    </td>
                                    <td class="p-2">
                                        <div class="bg-gym-secondary/20 border border-gym-secondary/30 rounded-lg p-3 text-center">
                                            <div class="text-gym-secondary font-medium text-sm">HIIT Training</div>
                                            <div class="text-gray-400 text-xs">Mike R.</div>
                                            <div class="text-gray-400 text-xs">18/20</div>
                                        </div>
                                    </td>
                                    <td class="p-2">
                                        <div class="bg-gym-accent/20 border border-gym-accent/30 rounded-lg p-3 text-center">
                                            <div class="text-gym-accent font-medium text-sm">Pilates</div>
                                            <div class="text-gray-400 text-xs">Emma L.</div>
                                            <div class="text-gray-400 text-xs">16/18</div>
                                        </div>
                                    </td>
                                    <td class="p-2">
                                        <div class="bg-gym-secondary/20 border border-gym-secondary/30 rounded-lg p-3 text-center">
                                            <div class="text-gym-secondary font-medium text-sm">HIIT Training</div>
                                            <div class="text-gray-400 text-xs">Mike R.</div>
                                            <div class="text-gray-400 text-xs">15/20</div>
                                        </div>
                                    </td>
                                    <td class="p-2">
                                        <div class="bg-purple-500/20 border border-purple-500/30 rounded-lg p-3 text-center">
                                            <div class="text-purple-400 font-medium text-sm">Zumba</div>
                                            <div class="text-gray-400 text-xs">Lisa M.</div>
                                            <div class="text-gray-400 text-xs">22/25</div>
                                        </div>
                                    </td>
                                    <td class="p-2"></td>
                                </tr>
                                <tr>
                                    <td class="p-4 text-gray-400 font-medium">6:00 PM</td>
                                    <td class="p-2">
                                        <div class="bg-red-500/20 border border-red-500/30 rounded-lg p-3 text-center">
                                            <div class="text-red-400 font-medium text-sm">CrossFit</div>
                                            <div class="text-gray-400 text-xs">John D.</div>
                                            <div class="text-gray-400 text-xs">12/12</div>
                                        </div>
                                    </td>
                                    <td class="p-2">
                                        <div class="bg-gym-primary/20 border border-gym-primary/30 rounded-lg p-3 text-center">
                                            <div class="text-gym-primary font-medium text-sm">Yoga Flow</div>
                                            <div class="text-gray-400 text-xs">Sarah K.</div>
                                            <div class="text-gray-400 text-xs">18/20</div>
                                        </div>
                                    </td>
                                    <td class="p-2">
                                        <div class="bg-red-500/20 border border-red-500/30 rounded-lg p-3 text-center">
                                            <div class="text-red-400 font-medium text-sm">CrossFit</div>
                                            <div class="text-gray-400 text-xs">John D.</div>
                                            <div class="text-gray-400 text-xs">11/12</div>
                                        </div>
                                    </td>
                                    <td class="p-2">
                                        <div class="bg-gym-primary/20 border border-gym-primary/30 rounded-lg p-3 text-center">
                                            <div class="text-gym-primary font-medium text-sm">Yoga Flow</div>
                                            <div class="text-gray-400 text-xs">Sarah K.</div>
                                            <div class="text-gray-400 text-xs">15/20</div>
                                        </div>
                                    </td>
                                    <td class="p-2">
                                        <div class="bg-red-500/20 border border-red-500/30 rounded-lg p-3 text-center">
                                            <div class="text-red-400 font-medium text-sm">CrossFit</div>
                                            <div class="text-gray-400 text-xs">John D.</div>
                                            <div class="text-gray-400 text-xs">12/12</div>
                                        </div>
                                    </td>
                                    <td class="p-2"></td>
                                    <td class="p-2">
                                        <div class="bg-gym-accent/20 border border-gym-accent/30 rounded-lg p-3 text-center">
                                            <div class="text-gym-accent font-medium text-sm">Meditation</div>
                                            <div class="text-gray-400 text-xs">Anna P.</div>
                                            <div class="text-gray-400 text-xs">8/15</div>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Class Details -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mt-8">
                    <!-- Popular Classes -->
                    <div class="bg-gym-dark rounded-xl p-6 border border-gray-800">
                        <h3 class="text-xl font-bold text-white mb-6">Popular Classes</h3>
                        <div class="space-y-4">
                            <div class="flex items-center justify-between p-4 bg-gray-800/50 rounded-lg">
                                <div class="flex items-center space-x-4">
                                    <div class="w-12 h-12 bg-gym-secondary rounded-full flex items-center justify-center">
                                        <i class="fas fa-fire text-white"></i>
                                    </div>
                                    <div>
                                        <div class="text-white font-medium">HIIT Training</div>
                                        <div class="text-gray-400 text-sm">High intensity workout</div>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <div class="text-gym-secondary font-bold">95%</div>
                                    <div class="text-gray-400 text-sm">Avg. Full</div>
                                </div>
                            </div>

                            <div class="flex items-center justify-between p-4 bg-gray-800/50 rounded-lg">
                                <div class="flex items-center space-x-4">
                                    <div class="w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center">
                                        <i class="fas fa-music text-white"></i>
                                    </div>
                                    <div>
                                        <div class="text-white font-medium">Zumba</div>
                                        <div class="text-gray-400 text-sm">Dance fitness class</div>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <div class="text-purple-400 font-bold">88%</div>
                                    <div class="text-gray-400 text-sm">Avg. Full</div>
                                </div>
                            </div>

                            <div class="flex items-center justify-between p-4 bg-gray-800/50 rounded-lg">
                                <div class="flex items-center space-x-4">
                                    <div class="w-12 h-12 bg-gym-primary rounded-full flex items-center justify-center">
                                        <i class="fas fa-leaf text-white"></i>
                                    </div>
                                    <div>
                                        <div class="text-white font-medium">Yoga Flow</div>
                                        <div class="text-gray-400 text-sm">Mindful movement</div>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <div class="text-gym-primary font-bold">82%</div>
                                    <div class="text-gray-400 text-sm">Avg. Full</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Instructors -->
                    <div class="bg-gym-dark rounded-xl p-6 border border-gray-800">
                        <h3 class="text-xl font-bold text-white mb-6">Top Instructors</h3>
                        <div class="space-y-4">
                            <div class="flex items-center space-x-4 p-4 bg-gray-800/50 rounded-lg">
                                <div class="w-12 h-12 bg-gym-primary rounded-full flex items-center justify-center">
                                    <i class="fas fa-user text-white"></i>
                                </div>
                                <div class="flex-1">
                                    <div class="text-white font-medium">Mike Rodriguez</div>
                                    <div class="text-gray-400 text-sm">HIIT & CrossFit Specialist</div>
                                </div>
                                <div class="text-right">
                                    <div class="text-gym-primary">4.9★</div>
                                    <div class="text-gray-400 text-sm">156 reviews</div>
                                </div>
                            </div>

                            <div class="flex items-center space-x-4 p-4 bg-gray-800/50 rounded-lg">
                                <div class="w-12 h-12 bg-gym-secondary rounded-full flex items-center justify-center">
                                    <i class="fas fa-user text-white"></i>
                                </div>
                                <div class="flex-1">
                                    <div class="text-white font-medium">Sarah Kim</div>
                                    <div class="text-gray-400 text-sm">Yoga & Meditation</div>
                                </div>
                                <div class="text-right">
                                    <div class="text-gym-secondary">4.8★</div>
                                    <div class="text-gray-400 text-sm">203 reviews</div>
                                </div>
                            </div>

                            <div class="flex items-center space-x-4 p-4 bg-gray-800/50 rounded-lg">
                                <div class="w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center">
                                    <i class="fas fa-user text-white"></i>
                                </div>
                                <div class="flex-1">
                                    <div class="text-white font-medium">Lisa Martinez</div>
                                    <div class="text-gray-400 text-sm">Zumba & Dance Fitness</div>
                                </div>
                                <div class="text-right">
                                    <div class="text-purple-400">4.9★</div>
                                    <div class="text-gray-400 text-sm">189 reviews</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Statistics Section -->
            <div id="statistics-section" class="section hidden">
                <div class="mb-8">
                    <h2 class="text-2xl font-bold text-white mb-2">Statistics & Analytics</h2>
                    <p class="text-gray-400">Track gym performance and member engagement</p>
                </div>

                <!-- Key Metrics -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                    <div class="bg-gym-dark rounded-xl p-6 border border-gray-800">
                        <div class="flex items-center justify-between mb-4">
                            <div class="w-12 h-12 bg-gym-primary/20 rounded-lg flex items-center justify-center">
                                <i class="fas fa-users text-gym-primary text-xl"></i>
                            </div>
                            <span class="text-gym-primary text-sm">+12%</span>
                        </div>
                        <div class="text-3xl font-bold text-white mb-1">1,247</div>
                        <div class="text-gray-400 text-sm">Total Members</div>
                        <div class="w-full bg-gray-800 rounded-full h-2 mt-3">
                            <div class="bg-gym-primary h-2 rounded-full" style="width: 85%"></div>
                        </div>
                    </div>

                    <div class="bg-gym-dark rounded-xl p-6 border border-gray-800">
                        <div class="flex items-center justify-between mb-4">
                            <div class="w-12 h-12 bg-gym-secondary/20 rounded-lg flex items-center justify-center">
                                <i class="fas fa-chart-line text-gym-secondary text-xl"></i>
                            </div>
                            <span class="text-gym-secondary text-sm">+8%</span>
                        </div>
                        <div class="text-3xl font-bold text-white mb-1">89%</div>
                        <div class="text-gray-400 text-sm">Retention Rate</div>
                        <div class="w-full bg-gray-800 rounded-full h-2 mt-3">
                            <div class="bg-gym-secondary h-2 rounded-full" style="width: 89%"></div>
                        </div>
                    </div>

                    <div class="bg-gym-dark rounded-xl p-6 border border-gray-800">
                        <div class="flex items-center justify-between mb-4">
                            <div class="w-12 h-12 bg-gym-accent/20 rounded-lg flex items-center justify-center">
                                <i class="fas fa-calendar-check text-gym-accent text-xl"></i>
                            </div>
                            <span class="text-gym-accent text-sm">+15%</span>
                        </div>
                        <div class="text-3xl font-bold text-white mb-1">3.2</div>
                        <div class="text-gray-400 text-sm">Avg Visits/Week</div>
                        <div class="w-full bg-gray-800 rounded-full h-2 mt-3">
                            <div class="bg-gym-accent h-2 rounded-full" style="width: 64%"></div>
                        </div>
                    </div>

                    <div class="bg-gym-dark rounded-xl p-6 border border-gray-800">
                        <div class="flex items-center justify-between mb-4">
                            <div class="w-12 h-12 bg-gym-blue/20 rounded-lg flex items-center justify-center">
                                <i class="fas fa-dollar-sign text-gym-blue text-xl"></i>
                            </div>
                            <span class="text-gym-blue text-sm">+22%</span>
                        </div>
                        <div class="text-3xl font-bold text-white mb-1">$24.5K</div>
                        <div class="text-gray-400 text-sm">Monthly Revenue</div>
                        <div class="w-full bg-gray-800 rounded-full h-2 mt-3">
                            <div class="bg-gym-blue h-2 rounded-full" style="width: 78%"></div>
                        </div>
                    </div>
                </div>

                <!-- Charts and Analytics -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                    <!-- Membership Growth Chart -->
                    <div class="bg-gym-dark rounded-xl p-6 border border-gray-800">
                        <h3 class="text-xl font-bold text-white mb-6">Membership Growth</h3>
                        <div class="relative h-64">
                            <!-- Simulated Chart with CSS -->
                            <div class="absolute bottom-0 left-0 right-0 flex items-end justify-between h-full px-4">
                                <div class="flex flex-col items-center">
                                    <div class="bg-gym-primary rounded-t w-8 mb-2" style="height: 40%"></div>
                                    <span class="text-xs text-gray-400">Jan</span>
                                </div>
                                <div class="flex flex-col items-center">
                                    <div class="bg-gym-primary rounded-t w-8 mb-2" style="height: 55%"></div>
                                    <span class="text-xs text-gray-400">Feb</span>
                                </div>
                                <div class="flex flex-col items-center">
                                    <div class="bg-gym-primary rounded-t w-8 mb-2" style="height: 70%"></div>
                                    <span class="text-xs text-gray-400">Mar</span>
                                </div>
                                <div class="flex flex-col items-center">
                                    <div class="bg-gym-primary rounded-t w-8 mb-2" style="height: 45%"></div>
                                    <span class="text-xs text-gray-400">Apr</span>
                                </div>
                                <div class="flex flex-col items-center">
                                    <div class="bg-gym-primary rounded-t w-8 mb-2" style="height: 80%"></div>
                                    <span class="text-xs text-gray-400">May</span>
                                </div>
                                <div class="flex flex-col items-center">
                                    <div class="bg-gym-primary rounded-t w-8 mb-2" style="height: 90%"></div>
                                    <span class="text-xs text-gray-400">Jun</span>
                                </div>
                            </div>
                        </div>
                        <div class="flex items-center justify-between mt-4 text-sm">
                            <span class="text-gray-400">Growth Rate</span>
                            <span class="text-gym-primary font-medium">+12% this month</span>
                        </div>
                    </div>

                    <!-- Peak Hours -->
                    <div class="bg-gym-dark rounded-xl p-6 border border-gray-800">
                        <h3 class="text-xl font-bold text-white mb-6">Peak Hours</h3>
                        <div class="space-y-4">
                            <div class="flex items-center justify-between">
                                <span class="text-gray-400">6:00 - 8:00 AM</span>
                                <div class="flex items-center space-x-3">
                                    <div class="w-32 bg-gray-800 rounded-full h-2">
                                        <div class="bg-gym-primary h-2 rounded-full" style="width: 85%"></div>
                                    </div>
                                    <span class="text-white text-sm">85%</span>
                                </div>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-gray-400">12:00 - 2:00 PM</span>
                                <div class="flex items-center space-x-3">
                                    <div class="w-32 bg-gray-800 rounded-full h-2">
                                        <div class="bg-gym-secondary h-2 rounded-full" style="width: 65%"></div>
                                    </div>
                                    <span class="text-white text-sm">65%</span>
                                </div>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-gray-400">6:00 - 8:00 PM</span>
                                <div class="flex items-center space-x-3">
                                    <div class="w-32 bg-gray-800 rounded-full h-2">
                                        <div class="bg-gym-accent h-2 rounded-full" style="width: 95%"></div>
                                    </div>
                                    <span class="text-white text-sm">95%</span>
                                </div>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-gray-400">8:00 - 10:00 PM</span>
                                <div class="flex items-center space-x-3">
                                    <div class="w-32 bg-gray-800 rounded-full h-2">
                                        <div class="bg-purple-400 h-2 rounded-full" style="width: 45%"></div>
                                    </div>
                                    <span class="text-white text-sm">45%</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Member Demographics -->
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    <!-- Age Distribution -->
                    <div class="bg-gym-dark rounded-xl p-6 border border-gray-800">
                        <h3 class="text-lg font-semibold text-white mb-4">Age Distribution</h3>
                        <div class="space-y-3">
                            <div class="flex items-center justify-between">
                                <span class="text-gray-400">18-25</span>
                                <div class="flex items-center space-x-2">
                                    <div class="w-20 bg-gray-800 rounded-full h-2">
                                        <div class="bg-gym-primary h-2 rounded-full" style="width: 25%"></div>
                                    </div>
                                    <span class="text-white text-sm">25%</span>
                                </div>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-gray-400">26-35</span>
                                <div class="flex items-center space-x-2">
                                    <div class="w-20 bg-gray-800 rounded-full h-2">
                                        <div class="bg-gym-secondary h-2 rounded-full" style="width: 40%"></div>
                                    </div>
                                    <span class="text-white text-sm">40%</span>
                                </div>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-gray-400">36-45</span>
                                <div class="flex items-center space-x-2">
                                    <div class="w-20 bg-gray-800 rounded-full h-2">
                                        <div class="bg-gym-accent h-2 rounded-full" style="width: 20%"></div>
                                    </div>
                                    <span class="text-white text-sm">20%</span>
                                </div>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-gray-400">46+</span>
                                <div class="flex items-center space-x-2">
                                    <div class="w-20 bg-gray-800 rounded-full h-2">
                                        <div class="bg-purple-400 h-2 rounded-full" style="width: 15%"></div>
                                    </div>
                                    <span class="text-white text-sm">15%</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Membership Plans -->
                    <div class="bg-gym-dark rounded-xl p-6 border border-gray-800">
                        <h3 class="text-lg font-semibold text-white mb-4">Plan Distribution</h3>
                        <div class="space-y-4">
                            <div class="flex items-center justify-between p-3 bg-gym-primary/10 rounded-lg">
                                <div class="flex items-center space-x-3">
                                    <div class="w-3 h-3 bg-gym-primary rounded-full"></div>
                                    <span class="text-white">Premium</span>
                                </div>
                                <span class="text-gym-primary font-medium">45%</span>
                            </div>
                            <div class="flex items-center justify-between p-3 bg-gym-secondary/10 rounded-lg">
                                <div class="flex items-center space-x-3">
                                    <div class="w-3 h-3 bg-gym-secondary rounded-full"></div>
                                    <span class="text-white">Basic</span>
                                </div>
                                <span class="text-gym-secondary font-medium">35%</span>
                            </div>
                            <div class="flex items-center justify-between p-3 bg-gym-blue/10 rounded-lg">
                                <div class="flex items-center space-x-3">
                                    <div class="w-3 h-3 bg-gym-blue rounded-full"></div>
                                    <span class="text-white">Elite</span>
                                </div>
                                <span class="text-gym-blue font-medium">20%</span>
                            </div>
                        </div>
                    </div>

                    <!-- Top Achievements -->
                    <div class="bg-gym-dark rounded-xl p-6 border border-gray-800">
                        <h3 class="text-lg font-semibold text-white mb-4">Top Achievements</h3>
                        <div class="space-y-3">
                            <div class="flex items-center space-x-3 p-3 bg-yellow-500/10 rounded-lg">
                                <i class="fas fa-trophy text-yellow-400"></i>
                                <div>
                                    <div class="text-white font-medium">100 Visits</div>
                                    <div class="text-gray-400 text-sm">89 members</div>
                                </div>
                            </div>
                            <div class="flex items-center space-x-3 p-3 bg-gym-primary/10 rounded-lg">
                                <i class="fas fa-medal text-gym-primary"></i>
                                <div>
                                    <div class="text-white font-medium">Consistency</div>
                                    <div class="text-gray-400 text-sm">156 members</div>
                                </div>
                            </div>
                            <div class="flex items-center space-x-3 p-3 bg-purple-500/10 rounded-lg">
                                <i class="fas fa-star text-purple-400"></i>
                                <div>
                                    <div class="text-white font-medium">Class Regular</div>
                                    <div class="text-gray-400 text-sm">203 members</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
        </div>
    </div>

    <!-- JavaScript for Navigation and Interactivity -->
    <script>
        // Theme Management
        class ThemeManager {
            constructor() {
                this.currentTheme = localStorage.getItem('gym-theme') || 'dark';
                this.init();
            }

            init() {
                // Set initial theme
                this.setTheme(this.currentTheme);

                // Add event listener to toggle button
                const toggleButton = document.getElementById('theme-toggle');
                if (toggleButton) {
                    toggleButton.addEventListener('click', () => this.toggleTheme());
                }
            }

            setTheme(theme) {
                this.currentTheme = theme;
                document.body.setAttribute('data-theme', theme);
                localStorage.setItem('gym-theme', theme);

                // Update theme toggle button appearance
                this.updateThemeToggleUI();

                // Update dynamic styles that need theme-aware colors
                this.updateDynamicStyles();
            }

            toggleTheme() {
                const newTheme = this.currentTheme === 'dark' ? 'light' : 'dark';
                this.setTheme(newTheme);
            }

            updateThemeToggleUI() {
                const toggleButton = document.getElementById('theme-toggle');
                const themeIcon = document.getElementById('theme-icon');

                if (toggleButton && themeIcon) {
                    if (this.currentTheme === 'light') {
                        toggleButton.classList.add('active');
                        toggleButton.title = 'Switch to dark theme';
                        toggleButton.setAttribute('aria-label', 'Switch to dark theme');
                        themeIcon.className = 'fas fa-sun header-toggle-icon';
                    } else {
                        toggleButton.classList.remove('active');
                        toggleButton.title = 'Switch to light theme';
                        toggleButton.setAttribute('aria-label', 'Switch to light theme');
                        themeIcon.className = 'fas fa-moon header-toggle-icon';
                    }

                    // Add a subtle feedback animation
                    toggleButton.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        toggleButton.style.transform = '';
                    }, 150);
                }
            }

            updateDynamicStyles() {
                // Update any elements that need special handling for theme changes
                const searchInput = document.querySelector('input[placeholder="Search members..."]');
                if (searchInput) {
                    searchInput.style.setProperty('--placeholder-color',
                        this.currentTheme === 'dark' ? '#9ca3af' : '#64748b');
                }

                // Update notification dropdown and other dynamic elements
                this.updateNotificationStyles();
                this.updateCardStyles();
                this.updateNavigationStyles();
                this.updateFormElements();
                this.updateAllTextElements();
            }

            updateNotificationStyles() {
                const dropdown = document.getElementById('notification-dropdown');
                if (dropdown) {
                    dropdown.style.backgroundColor = 'var(--card-bg)';
                    dropdown.style.borderColor = 'var(--border-primary)';
                }
            }

            updateCardStyles() {
                // Update all cards and sections to use CSS custom properties
                const cards = document.querySelectorAll('.bg-gym-dark, .bg-gray-800');
                cards.forEach(card => {
                    card.style.backgroundColor = 'var(--card-bg)';
                    card.style.borderColor = 'var(--border-primary)';
                });
            }

            updateNavigationStyles() {
                // Update navigation links
                const navLinks = document.querySelectorAll('.nav-link');
                navLinks.forEach(link => {
                    if (!link.classList.contains('active')) {
                        link.style.color = 'var(--text-secondary)';
                    } else {
                        link.style.color = 'var(--text-primary)';
                        link.style.backgroundColor = 'var(--hover-bg)';
                    }
                });
            }

            updateFormElements() {
                // Update form inputs and selects
                const inputs = document.querySelectorAll('input, select, textarea');
                inputs.forEach(input => {
                    input.style.backgroundColor = 'var(--input-bg)';
                    input.style.borderColor = 'var(--border-secondary)';
                    input.style.color = 'var(--text-primary)';
                });
            }

            updateAllTextElements() {
                // Update text elements that weren't caught by other methods
                const textElements = document.querySelectorAll('.text-white, .text-gray-100, .text-gray-200');
                textElements.forEach(element => {
                    element.style.color = 'var(--text-primary)';
                });

                const mutedTextElements = document.querySelectorAll('.text-gray-400, .text-gray-500');
                mutedTextElements.forEach(element => {
                    element.style.color = 'var(--text-muted)';
                });

                const secondaryTextElements = document.querySelectorAll('.text-gray-300');
                secondaryTextElements.forEach(element => {
                    element.style.color = 'var(--text-secondary)';
                });
            }
        }

        // Fullscreen Management
        class FullscreenManager {
            constructor() {
                this.isFullscreen = false;
                this.init();
            }

            // Check if fullscreen is supported
            isSupported() {
                return !!(document.documentElement.requestFullscreen ||
                         document.documentElement.mozRequestFullScreen ||
                         document.documentElement.webkitRequestFullscreen ||
                         document.documentElement.msRequestFullscreen);
            }

            // Enter fullscreen mode
            async enterFullscreen() {
                const element = document.documentElement;

                try {
                    if (element.requestFullscreen) {
                        await element.requestFullscreen();
                    } else if (element.mozRequestFullScreen) {
                        await element.mozRequestFullScreen();
                    } else if (element.webkitRequestFullscreen) {
                        await element.webkitRequestFullscreen();
                    } else if (element.msRequestFullscreen) {
                        await element.msRequestFullscreen();
                    } else {
                        throw new Error('Fullscreen not supported');
                    }
                } catch (error) {
                    console.warn('Enter fullscreen failed:', error);
                    this.showFallbackMessage();
                }
            }

            // Exit fullscreen mode
            async exitFullscreen() {
                try {
                    if (document.exitFullscreen) {
                        await document.exitFullscreen();
                    } else if (document.mozCancelFullScreen) {
                        await document.mozCancelFullScreen();
                    } else if (document.webkitExitFullscreen) {
                        await document.webkitExitFullscreen();
                    } else if (document.msExitFullscreen) {
                        await document.msExitFullscreen();
                    } else {
                        throw new Error('Exit fullscreen not supported');
                    }
                } catch (error) {
                    console.warn('Exit fullscreen failed:', error);
                    this.showFallbackMessage();
                }
            }

            // Check if currently in fullscreen
            isCurrentlyFullscreen() {
                return !!(document.fullscreenElement ||
                         document.mozFullScreenElement ||
                         document.webkitFullscreenElement ||
                         document.msFullscreenElement);
            }

            // Toggle fullscreen mode
            async toggleFullscreen() {
                if (this.isCurrentlyFullscreen()) {
                    await this.exitFullscreen();
                } else {
                    await this.enterFullscreen();
                }
            }

            // Update UI state
            updateUI() {
                const isFullscreen = this.isCurrentlyFullscreen();
                const button = document.getElementById('fullscreen-toggle');
                const fullscreenIcon = document.getElementById('fullscreen-icon');

                if (button && fullscreenIcon) {
                    // Update button appearance and accessibility
                    if (isFullscreen) {
                        button.classList.add('active');
                        button.title = 'Exit fullscreen';
                        button.setAttribute('aria-label', 'Exit fullscreen mode');
                        fullscreenIcon.className = 'fas fa-compress header-toggle-icon';
                    } else {
                        button.classList.remove('active');
                        button.title = 'Enter fullscreen';
                        button.setAttribute('aria-label', 'Enter fullscreen mode');
                        fullscreenIcon.className = 'fas fa-expand header-toggle-icon';
                    }

                    // Add a subtle feedback animation
                    button.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        button.style.transform = '';
                    }, 150);
                }

                this.isFullscreen = isFullscreen;
            }

            // Show fallback message for unsupported browsers
            showFallbackMessage() {
                // Create a temporary notification
                const notification = document.createElement('div');
                notification.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: var(--card-bg);
                    color: var(--text-primary);
                    padding: 12px 16px;
                    border-radius: 8px;
                    border: 1px solid var(--border-primary);
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                    z-index: 10000;
                    font-size: 14px;
                    max-width: 300px;
                    font-family: inherit;
                `;
                notification.textContent = 'Fullscreen not supported. Try pressing F11 instead.';

                document.body.appendChild(notification);

                // Remove notification after 4 seconds
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 4000);
            }

            // Initialize fullscreen manager
            init() {
                const button = document.getElementById('fullscreen-toggle');

                if (!button) {
                    console.warn('Fullscreen toggle button not found');
                    return;
                }

                // Add click event listener
                button.addEventListener('click', (e) => {
                    e.preventDefault();
                    e.stopPropagation();

                    if (!this.isSupported()) {
                        this.showFallbackMessage();
                        return;
                    }

                    this.toggleFullscreen();
                });

                // Listen for fullscreen change events
                const fullscreenEvents = [
                    'fullscreenchange',
                    'mozfullscreenchange',
                    'webkitfullscreenchange',
                    'msfullscreenchange'
                ];

                fullscreenEvents.forEach(event => {
                    document.addEventListener(event, () => {
                        this.updateUI();
                    });
                });

                // Initial UI update
                this.updateUI();

                // Handle keyboard shortcuts (F11 detection)
                document.addEventListener('keydown', (e) => {
                    if (e.key === 'F11') {
                        // F11 was pressed, update UI after a short delay
                        setTimeout(() => {
                            this.updateUI();
                        }, 100);
                    }
                });
            }
        }

        // Initialize theme and fullscreen managers when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            window.themeManager = new ThemeManager();
            window.fullscreenManager = new FullscreenManager();
        });

        // Notification System Data
        let notifications = [
            {
                id: 1,
                type: 'member',
                title: 'New Member Registration',
                message: 'Sarah Johnson has registered for a Basic membership',
                timestamp: new Date(Date.now() - 2 * 60 * 1000), // 2 minutes ago
                read: false,
                icon: 'fa-user-plus',
                color: 'gym-secondary'
            },
            {
                id: 2,
                type: 'payment',
                title: 'Payment Overdue',
                message: 'Lisa Wilson\'s payment is 3 days overdue ($59.99)',
                timestamp: new Date(Date.now() - 15 * 60 * 1000), // 15 minutes ago
                read: false,
                icon: 'fa-exclamation-triangle',
                color: 'red-500'
            },
            {
                id: 3,
                type: 'class',
                title: 'Class Booking Confirmed',
                message: 'HIIT Training class is fully booked (20/20)',
                timestamp: new Date(Date.now() - 45 * 60 * 1000), // 45 minutes ago
                read: false,
                icon: 'fa-calendar-check',
                color: 'gym-primary'
            },
            {
                id: 4,
                type: 'payment',
                title: 'Payment Successful',
                message: 'Mike Davis paid $99.99 for Elite membership',
                timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
                read: true,
                icon: 'fa-credit-card',
                color: 'gym-accent'
            },
            {
                id: 5,
                type: 'maintenance',
                title: 'Equipment Maintenance',
                message: 'Treadmill #3 requires scheduled maintenance',
                timestamp: new Date(Date.now() - 3 * 60 * 60 * 1000), // 3 hours ago
                read: true,
                icon: 'fa-wrench',
                color: 'gym-secondary'
            },
            {
                id: 6,
                type: 'system',
                title: 'System Update',
                message: 'Gym management system updated to v2.1.0',
                timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000), // 6 hours ago
                read: true,
                icon: 'fa-info-circle',
                color: 'gym-blue'
            }
        ];

        // Navigation functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Mobile menu toggle
            const openSidebar = document.getElementById('open-sidebar');
            const closeSidebar = document.getElementById('close-sidebar');
            const sidebar = document.getElementById('sidebar');
            const overlay = document.getElementById('mobile-menu-overlay');

            // Initialize sidebar state
            function initializeSidebar() {
                // Ensure sidebar is hidden on mobile by default
                if (window.innerWidth < 1024) {
                    sidebar.classList.add('-translate-x-full');
                    sidebar.classList.remove('translate-x-0');
                    overlay.classList.add('hidden');
                    document.body.classList.remove('sidebar-open');
                    document.body.style.overflow = '';
                }
            }

            // Initialize on load
            initializeSidebar();

            // Re-initialize on window resize
            window.addEventListener('resize', function() {
                if (window.innerWidth >= 1024) {
                    // Desktop: ensure sidebar is visible and overlay is hidden
                    sidebar.classList.remove('-translate-x-full', 'translate-x-0');
                    overlay.classList.add('hidden');
                    document.body.classList.remove('sidebar-open');
                    document.body.style.overflow = '';
                } else {
                    // Mobile: ensure sidebar is hidden unless explicitly opened
                    if (!sidebar.classList.contains('translate-x-0')) {
                        sidebar.classList.add('-translate-x-full');
                        overlay.classList.add('hidden');
                        document.body.classList.remove('sidebar-open');
                        document.body.style.overflow = '';
                    }
                }
            });

            // Check if elements exist
            if (!openSidebar || !closeSidebar || !sidebar || !overlay) {
                console.error('Missing sidebar elements:', {
                    openSidebar: !!openSidebar,
                    closeSidebar: !!closeSidebar,
                    sidebar: !!sidebar,
                    overlay: !!overlay
                });
                return;
            }

            openSidebar.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                sidebar.classList.remove('-translate-x-full');
                sidebar.classList.add('translate-x-0');
                overlay.classList.remove('hidden');
                document.body.classList.add('sidebar-open');
                // Ensure sidebar scrolling works on mobile
                document.body.style.overflow = 'hidden';
            });

            closeSidebar.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                sidebar.classList.add('-translate-x-full');
                sidebar.classList.remove('translate-x-0');
                overlay.classList.add('hidden');
                document.body.classList.remove('sidebar-open');
                // Restore body scrolling
                document.body.style.overflow = '';
            });

            overlay.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                sidebar.classList.add('-translate-x-full');
                sidebar.classList.remove('translate-x-0');
                overlay.classList.add('hidden');
                document.body.classList.remove('sidebar-open');
                // Restore body scrolling
                document.body.style.overflow = '';
            });

            // Navigation links
            const navLinks = document.querySelectorAll('.nav-link');
            const sections = document.querySelectorAll('.section');

            navLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();

                    // Remove active class from all links
                    navLinks.forEach(l => l.classList.remove('active'));

                    // Add active class to clicked link
                    this.classList.add('active');

                    // Hide all sections
                    sections.forEach(section => section.classList.add('hidden'));

                    // Show target section
                    const target = this.getAttribute('href').substring(1);
                    const targetSection = document.getElementById(target + '-section');
                    if (targetSection) {
                        targetSection.classList.remove('hidden');
                    }

                    // Update header title
                    const headerTitle = document.querySelector('header h1');
                    const linkText = this.textContent.trim();
                    headerTitle.textContent = linkText;

                    // Close mobile menu
                    if (window.innerWidth < 1024) {
                        sidebar.classList.add('-translate-x-full');
                        sidebar.classList.remove('translate-x-0');
                        overlay.classList.add('hidden');
                        document.body.classList.remove('sidebar-open');
                        // Restore body scrolling
                        document.body.style.overflow = '';
                    }
                });
            });

            // Notification System
            const notificationBtn = document.getElementById('notification-btn');
            const notificationDropdown = document.getElementById('notification-dropdown');
            const notificationBadge = document.getElementById('notification-badge');
            const notificationsList = document.getElementById('notifications-list');
            const markAllReadBtn = document.getElementById('mark-all-read');
            const viewAllBtn = document.getElementById('view-all-notifications');

            // Format timestamp to relative time
            function formatTimestamp(timestamp) {
                const now = new Date();
                const diff = now - timestamp;
                const minutes = Math.floor(diff / (1000 * 60));
                const hours = Math.floor(diff / (1000 * 60 * 60));
                const days = Math.floor(diff / (1000 * 60 * 60 * 24));

                if (minutes < 1) return 'Just now';
                if (minutes < 60) return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
                if (hours < 24) return `${hours} hour${hours > 1 ? 's' : ''} ago`;
                return `${days} day${days > 1 ? 's' : ''} ago`;
            }

            // Update notification badge count
            function updateNotificationBadge() {
                const unreadCount = notifications.filter(n => !n.read).length;
                if (unreadCount > 0) {
                    notificationBadge.textContent = unreadCount;
                    notificationBadge.classList.remove('hidden');
                } else {
                    notificationBadge.classList.add('hidden');
                }
            }

            // Render notifications
            function renderNotifications() {
                const visibleNotifications = notifications.slice(0, 7); // Show max 7 notifications
                
                notificationsList.innerHTML = visibleNotifications.length > 0 ? visibleNotifications.map(notification => `
                    <div class="notification-item ${!notification.read ? 'unread' : ''}"
                         data-id="${notification.id}">
                        <div class="flex items-start space-x-3">
                            <div class="notification-icon-container bg-${notification.color}/20">
                                <i class="fas ${notification.icon} text-${notification.color}"></i>
                            </div>
                            <div class="flex-1 min-w-0">
                                <div class="flex items-center justify-between">
                                    <h4 class="notification-title">${notification.title}</h4>
                                    ${!notification.read ? '<div class="unread-dot"></div>' : ''}
                                </div>
                                <p class="notification-message">${notification.message}</p>
                                <p class="notification-timestamp">${formatTimestamp(notification.timestamp)}</p>
                            </div>
                        </div>
                    </div>
                `).join('') : `
                    <div class="notifications-empty-state">
                        <i class="fas fa-bell-slash"></i>
                        <p>No new notifications</p>
                    </div>
                `;

                updateNotificationBadge();
            }

            // Toggle notification dropdown
            notificationBtn.addEventListener('click', function(e) {
                e.stopPropagation();

                // Position dropdown relative to notification button
                const buttonRect = notificationBtn.getBoundingClientRect();
                const dropdownWidth = 320; // 80 * 4 (w-80 in Tailwind)

                // Calculate position
                let rightPosition = window.innerWidth - buttonRect.right;
                let topPosition = buttonRect.bottom + 8; // 8px gap

                // Ensure dropdown doesn't go off-screen
                if (rightPosition + dropdownWidth > window.innerWidth) {
                    rightPosition = 16; // 1rem minimum margin
                }

                if (topPosition + 400 > window.innerHeight) {
                    topPosition = buttonRect.top - 400 - 8; // Show above if not enough space below
                }

                // Apply positioning
                notificationDropdown.style.right = rightPosition + 'px';
                notificationDropdown.style.top = topPosition + 'px';

                notificationDropdown.classList.toggle('hidden');
                if (!notificationDropdown.classList.contains('hidden')) {
                    renderNotifications();
                }
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', function(e) {
                if (!notificationDropdown.contains(e.target) && !notificationBtn.contains(e.target)) {
                    notificationDropdown.classList.add('hidden');
                }
            });

            // Mark notification as read when clicked
            notificationsList.addEventListener('click', function(e) {
                const notificationItem = e.target.closest('.notification-item');
                if (notificationItem) {
                    const notificationId = parseInt(notificationItem.dataset.id);
                    const notification = notifications.find(n => n.id === notificationId);
                    if (notification && !notification.read) {
                        notification.read = true;
                        renderNotifications();
                    }
                }
            });

            // Mark all notifications as read
            markAllReadBtn.addEventListener('click', function() {
                notifications.forEach(notification => {
                    notification.read = true;
                });
                renderNotifications();
            });

            // View all notifications (simulate navigation)
            viewAllBtn.addEventListener('click', function() {
                notificationDropdown.classList.add('hidden');
                // Here you would typically navigate to a dedicated notifications page
                alert('Navigate to full notifications page');
            });

            // Simulate new notifications
            function addNewNotification(type, title, message, icon, color) {
                const newNotification = {
                    id: Date.now(),
                    type: type,
                    title: title,
                    message: message,
                    timestamp: new Date(),
                    read: false,
                    icon: icon,
                    color: color
                };
                notifications.unshift(newNotification);
                updateNotificationBadge();

                // Show brief animation on bell icon
                notificationBtn.classList.add('animate-bounce');
                setTimeout(() => {
                    notificationBtn.classList.remove('animate-bounce');
                }, 1000);
            }

            // Simulate receiving new notifications periodically
            setInterval(() => {
                const randomNotifications = [
                    {
                        type: 'member',
                        title: 'New Check-in',
                        message: 'Alex Thompson just checked in',
                        icon: 'fa-sign-in-alt',
                        color: 'gym-primary'
                    },
                    {
                        type: 'class',
                        title: 'Class Cancelled',
                        message: 'Evening Yoga class has been cancelled',
                        icon: 'fa-calendar-times',
                        color: 'red-500'
                    },
                    {
                        type: 'payment',
                        title: 'Payment Reminder',
                        message: 'Monthly payment due in 3 days',
                        icon: 'fa-clock',
                        color: 'gym-secondary'
                    },
                    {
                        type: 'system',
                        title: 'System Maintenance',
                        message: 'Scheduled maintenance tonight at 2 AM',
                        icon: 'fa-tools',
                        color: 'gym-blue'
                    }
                ];

                if (Math.random() < 0.3) { // 30% chance every interval
                    const randomNotif = randomNotifications[Math.floor(Math.random() * randomNotifications.length)];
                    addNewNotification(randomNotif.type, randomNotif.title, randomNotif.message, randomNotif.icon, randomNotif.color);
                }
            }, 30000); // Every 30 seconds

            // Initialize notification system
            updateNotificationBadge();

            // Sidebar Scrolling Enhancements
            const sidebarScroll = document.querySelector('.sidebar-scroll');

            // Add smooth scrolling behavior
            if (sidebarScroll) {
                // Ensure active navigation item is visible when sidebar opens
                function scrollToActiveItem() {
                    const activeLink = sidebar.querySelector('.nav-link.active');
                    if (activeLink && sidebarScroll) {
                        setTimeout(() => {
                            const linkRect = activeLink.getBoundingClientRect();
                            const containerRect = sidebarScroll.getBoundingClientRect();

                            if (linkRect.top < containerRect.top || linkRect.bottom > containerRect.bottom) {
                                activeLink.scrollIntoView({
                                    behavior: 'smooth',
                                    block: 'center'
                                });
                            }
                        }, 300); // Wait for sidebar animation to complete
                    }
                }

                // Scroll to active item when sidebar opens on mobile
                openSidebar.addEventListener('click', scrollToActiveItem);

                // Enhanced scroll indicators with auto-hide scrollbar
                let scrollTimeout;
                let isScrolling = false;

                sidebarScroll.addEventListener('scroll', function() {
                    // Add scrolling class for visual feedback and scrollbar visibility
                    if (!isScrolling) {
                        this.classList.add('scrolling');
                        isScrolling = true;
                    }

                    clearTimeout(scrollTimeout);
                    scrollTimeout = setTimeout(() => {
                        this.classList.remove('scrolling');
                        isScrolling = false;
                    }, 1500); // Hide scrollbar after 1.5 seconds of no scrolling
                });

                // Show scrollbar on hover for better UX
                sidebarScroll.addEventListener('mouseenter', function() {
                    if (!isScrolling) {
                        this.classList.add('scrolling');
                    }
                });

                sidebarScroll.addEventListener('mouseleave', function() {
                    if (!isScrolling) {
                        clearTimeout(scrollTimeout);
                        scrollTimeout = setTimeout(() => {
                            this.classList.remove('scrolling');
                        }, 500); // Quick hide when mouse leaves
                    }
                });
            }

            // Handle window resize to ensure proper sidebar behavior
            window.addEventListener('resize', function() {
                if (window.innerWidth >= 1024) {
                    // Desktop view - ensure body scroll is restored
                    document.body.style.overflow = '';
                    overlay.classList.add('hidden');
                }
            });

            // Add notification triggers and navigation for quick actions
            const quickActionButtons = document.querySelectorAll('.group');
            quickActionButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const buttonText = this.textContent.trim();

                    // Navigation functionality
                    if (buttonText.includes('Add New Member')) {
                        document.querySelector('a[href="#registration"]').click();
                        setTimeout(() => {
                            addNewNotification('member', 'Member Registration Started', 'New member registration form opened', 'fa-user-plus', 'gym-secondary');
                        }, 1000);
                    } else if (buttonText.includes('Quick Check-in')) {
                        document.querySelector('a[href="#checkin"]').click();
                        setTimeout(() => {
                            addNewNotification('checkin', 'Check-in System Accessed', 'Quick check-in interface opened', 'fa-sign-in-alt', 'gym-primary');
                        }, 1000);
                    } else if (buttonText.includes('Schedule Class')) {
                        document.querySelector('a[href="#schedule"]').click();
                        setTimeout(() => {
                            addNewNotification('class', 'Class Scheduler Opened', 'Class scheduling interface accessed', 'fa-calendar-plus', 'gym-accent');
                        }, 1000);
                    } else if (buttonText.includes('View Reports')) {
                        document.querySelector('a[href="#statistics"]').click();
                        setTimeout(() => {
                            addNewNotification('system', 'Reports Accessed', 'Statistics and analytics dashboard opened', 'fa-chart-line', 'purple-500');
                        }, 1000);
                    }
                });
            });

            // Quick actions functionality (merged with notification triggers above)

            // Form validation styling
            const forms = document.querySelectorAll('form');
            forms.forEach(form => {
                const inputs = form.querySelectorAll('input[required], select[required]');
                inputs.forEach(input => {
                    input.addEventListener('blur', function() {
                        if (this.value.trim() === '') {
                            this.classList.add('border-red-500');
                            this.classList.remove('border-gray-700');
                        } else {
                            this.classList.remove('border-red-500');
                            this.classList.add('border-gray-700');
                        }
                    });
                });
            });

            // Member search functionality
            const searchInputs = document.querySelectorAll('input[placeholder*="Search"]');
            searchInputs.forEach(input => {
                input.addEventListener('input', function() {
                    // Simulate search functionality
                    console.log('Searching for:', this.value);
                });
            });

            // Check-in functionality simulation
            const checkinInput = document.querySelector('input[placeholder*="Member ID"]');
            if (checkinInput) {
                checkinInput.addEventListener('input', function() {
                    const memberInfo = document.getElementById('member-info');
                    if (this.value.length >= 5) {
                        memberInfo.classList.remove('hidden');
                    } else {
                        memberInfo.classList.add('hidden');
                    }
                });
            }

            // Membership plan selection
            const planRadios = document.querySelectorAll('input[name="membership"]');
            planRadios.forEach(radio => {
                radio.addEventListener('change', function() {
                    // Update UI based on selected plan
                    console.log('Selected plan:', this.value);
                });
            });

            // Table row hover effects
            const tableRows = document.querySelectorAll('tbody tr');
            tableRows.forEach(row => {
                row.addEventListener('mouseenter', function() {
                    this.classList.add('bg-gray-800/25');
                });
                row.addEventListener('mouseleave', function() {
                    this.classList.remove('bg-gray-800/25');
                });
            });

            // Note: Notification badge animation is now handled by the notification system above
        });

        // CSS for active navigation state
        const style = document.createElement('style');
        style.textContent = `
            .nav-link.active {
                background-color: rgba(16, 185, 129, 0.2);
                color: #33AADA;
                border-left: 3px solid #33AADA;
                padding-left: 9px;
            }

            .nav-link:not(.active) {
                color: #9CA3AF;
            }

            .nav-link:not(.active):hover {
                background-color: rgba(75, 85, 99, 0.5);
                color: #F3F4F6;
            }

            .section {
                animation: fadeIn 0.3s ease-in-out;
            }

            @keyframes fadeIn {
                from { opacity: 0; transform: translateY(10px); }
                to { opacity: 1; transform: translateY(0); }
            }

            /* Layout fixes */
            .flex.h-screen {
                min-height: 100vh;
            }

            /* Ensure sidebar is properly positioned */
            @media (min-width: 1024px) {
                #sidebar {
                    position: relative !important;
                    transform: translateX(0) !important;
                }
            }

            /* Notification System Styles */
            /* REVISED Notification System Styles */
            #notification-dropdown {
                animation: slideDown 0.2s ease-out;
                z-index: 9999 !important;
                position: fixed !important;
                /* top, right are set by JS */
                will-change: transform, opacity;
                border-radius: 0.75rem; /* rounded-xl */
                background-color: var(--card-bg);
                border: 1px solid var(--border-primary);
                box-shadow: 0 10px 15px -3px var(--shadow-color), 0 4px 6px -2px var(--shadow-color);
                width: 320px; /* w-80 */
                /* Removed top, right, transform !important to allow JS positioning */
            }
            [data-theme="light"] #notification-dropdown {
                background-color: var(--card-bg) !important;
                border: 1px solid var(--border-primary) !important;
                box-shadow: 0 10px 15px -3px var(--shadow-color), 0 4px 6px -2px var(--shadow-color) !important;
            }

            #notification-dropdown.hidden {
                animation: slideUp 0.2s ease-in;
            }

            /* Ensure header has proper z-index */
            header { /* This was already here, seems fine */
                z-index: 1000 !important;
                position: relative !important;
            }

            /* Remove the ::before overlay for light theme if simplifying */
            [data-theme="light"] #notification-dropdown::before { /* This rule was already in your file, seems fine */
                display: none;
            }

            #notification-dropdown > div:first-child, /* Header */
            #notification-dropdown > div:last-child { /* Footer */
                padding: 0.75rem 1rem; /* Consistent padding */
                border-color: var(--border-secondary); /* Softer border */
            }
            #notification-dropdown > div:first-child { border-bottom: 1px solid var(--border-secondary); }
            #notification-dropdown > div:last-child { border-top: 1px solid var(--border-secondary); }

            #mark-all-read, #view-all-notifications {
                font-size: 0.875rem; /* text-sm */
                font-weight: 500; /* medium */
                color: var(--gym-primary);
                transition: color 0.2s ease;
                background: none; /* Ensure it looks like a link/text button */
                border: none;
                padding: 0;
                cursor: pointer;
            }
            #mark-all-read:hover, #view-all-notifications:hover {
                color: var(--gym-accent);
                text-decoration: underline;
            }

            #notifications-list {
                max-height: 320px; /* Approx 20rem */
                overflow-y: auto;
                padding: 0.25rem 0;
            }

            /* Notification Item General */
            .notification-item {
                padding: 0.75rem 1rem;
                border-bottom: 1px solid var(--border-secondary);
                transition: background-color 0.2s ease;
                cursor: pointer;
                background-color: var(--card-bg); /* Default for read items */
            }
            .notification-item:last-child { border-bottom: none; }

            .notification-item .notification-title { /* Title */
                color: var(--text-primary);
                font-weight: 500; /* medium (Tailwind default for font-medium) */
                font-size: 0.875rem; /* text-sm */
                margin-bottom: 0.125rem;
            }
            .notification-item .notification-message { /* Message class for clarity */
                color: var(--text-secondary);
                font-size: 0.875rem; /* text-sm */
                line-height: 1.35; /* Slightly tighter for better density */
                margin-top: 0.125rem; /* Reduced margin */
            }
            .notification-item .notification-timestamp { /* Timestamp class */
                color: var(--text-muted);
                font-size: 0.75rem; /* text-xs */
                margin-top: 0.25rem; 
            }

            .notification-item .notification-icon-container { /* Icon container */
                width: 2rem; /* w-8 */
                height: 2rem; /* h-8 */
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                flex-shrink: 0;
            }
            .notification-item .notification-icon-container i { /* Icon */
                font-size: 0.875rem; /* text-sm */
            }

            .notification-item .unread-dot { /* Unread dot */
                width: 0.5rem;
                height: 0.5rem;
                background-color: var(--gym-primary);
                border-radius: 9999px; /* rounded-full */
                margin-left: 0.75rem; /* More spacing */
                margin-top: 0.25rem;
                flex-shrink: 0;
            }

            /* Dark Theme Notification Item */
            [data-theme="dark"] .notification-item.unread {
                background-color: var(--bg-secondary);
            }
            [data-theme="dark"] .notification-item:hover {
                background-color: var(--hover-bg);
            }

            /* Light Theme Notification Item */
            [data-theme="light"] .notification-item {
                border-bottom-color: var(--border-primary);
            }
            [data-theme="light"] .notification-item.unread {
                background-color: var(--bg-secondary);
            }
            [data-theme="light"] .notification-item:hover {
                background-color: var(--hover-bg);
            }

            /* Empty state for notifications */
            .notifications-empty-state {
                padding: 1.5rem 1rem;
                text-align: center;
            }
            .notifications-empty-state i {
                font-size: 1.75rem;
                color: var(--text-muted);
                margin-bottom: 0.75rem;
            }
            .notifications-empty-state p {
                font-size: 0.875rem; /* text-sm */
                color: var(--text-muted);
            }

            /* Notification button container positioning */
            .relative {
                position: relative;
            }

            /* Ensure proper stacking context */
            #notification-btn {
                z-index: 1001;
                position: relative;
            }
            
            /* Profile section hover */
            .profile-section:hover {
                background-color: var(--hover-bg);
            }
            /* Ensure text color remains appropriate on hover */
            /* The text color for "Admin User" is already set to var(--text-primary) in HTML, so it will adapt. */
            /* The icon color inside profile-avatar is white, which is fine for both themes with current avatar bg. */





            @keyframes slideUp {
                from {
                    opacity: 1;
                    transform: translateY(0);
                }
                to {
                    opacity: 0;
                    transform: translateY(-10px);
                }
            }

            .line-clamp-2 {
                display: -webkit-box;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
                overflow: hidden;
            }

            .line-clamp-1 { /* Added for titles */
                display: -webkit-box;
                -webkit-line-clamp: 1;
                -webkit-box-orient: vertical;
                overflow: hidden;
                text-overflow: ellipsis;
            }

            /* Notification badge pulse animation */
            @keyframes notificationPulse {
                0%, 100% {
                    transform: scale(1);
                }
                50% {
                    transform: scale(1.1);
                }
            }

            .notification-pulse {
                animation: notificationPulse 2s infinite;
            }

            /* Mobile responsive adjustments for notifications */
            @media (max-width: 640px) {
                #notification-dropdown {
                    width: calc(100vw - 2rem) !important;
                    /* JS will position top and right/left */
                    /* Fallback if JS fails or for initial render before JS runs */
                    left: 1rem;
                    right: 1rem;
                    top: 4rem; 
                    /* transform: none !important; */ /* Allow JS to transform if needed */
                    max-width: none !important;
                }
            }

            @media (max-width: 768px) {
                #notification-dropdown {
                    width: calc(100vw - 2rem) !important;
                     /* JS will position top and right/left */
                    /* Fallback */
                    right: 1rem;
                    max-width: 400px !important;
                }
            }

            /* Enhanced Responsive Design */

            /* Tablet Responsive (768px and below) */
            @media (max-width: 768px) {
                /* Header improvements */
                header {
                    padding: 1rem !important;
                }

                header .flex.items-center.space-x-4 {
                    gap: 0.75rem;
                }



                /* Adjust header title */
                header h1 {
                    font-size: 1.5rem !important;
                }

                header p {
                    font-size: 0.75rem !important;
                }

                /* Grid improvements */
                .grid-cols-1.md\\:grid-cols-2.lg\\:grid-cols-4 {
                    grid-template-columns: repeat(2, 1fr);
                    gap: 1rem;
                }

                .grid-cols-1.md\\:grid-cols-3 {
                    grid-template-columns: repeat(2, 1fr);
                    gap: 1rem;
                }

                .grid-cols-1.lg\\:grid-cols-3 {
                    grid-template-columns: 1fr;
                    gap: 1.5rem;
                }

                /* Stats cards improvements */
                .stats-card {
                    padding: 1rem !important;
                }

                .stats-card .text-3xl {
                    font-size: 1.75rem !important;
                }

                /* Main content padding */
                main {
                    padding: 1rem !important;
                }

                /* Card padding adjustments */
                .p-8 {
                    padding: 1.5rem !important;
                }

                .p-6 {
                    padding: 1rem !important;
                }

                /* Typography scaling */
                .text-5xl {
                    font-size: 2.5rem !important;
                }

                .text-4xl {
                    font-size: 2rem !important;
                }

                /* Form improvements */
                .max-w-4xl {
                    max-width: 100% !important;
                    margin: 0 !important;
                }

                /* Table responsiveness */
                .overflow-x-auto {
                    -webkit-overflow-scrolling: touch;
                }

                /* Button improvements */
                .flex-col.sm\\:flex-row {
                    flex-direction: column !important;
                }

                /* Quick actions spacing */
                .space-y-3 > * + * {
                    margin-top: 0.5rem !important;
                }
            }

            /* Mobile Responsive (640px and below) */
            @media (max-width: 640px) {
                /* Header mobile layout */
                header {
                    padding: 0.75rem !important;
                }

                header .flex.items-center.justify-between {
                    gap: 0.5rem;
                }

                /* Hide search on very small screens */
                .hidden.sm\\:block {
                    display: none !important;
                }

                /* Single column layouts */
                .grid-cols-1.md\\:grid-cols-2.lg\\:grid-cols-4 {
                    grid-template-columns: 1fr !important;
                    gap: 0.75rem;
                }

                .grid-cols-1.md\\:grid-cols-3 {
                    grid-template-columns: 1fr !important;
                    gap: 0.75rem;
                }

                .grid-cols-1.md\\:grid-cols-2 {
                    grid-template-columns: 1fr !important;
                    gap: 0.75rem;
                }

                /* Typography mobile scaling */
                .text-3xl {
                    font-size: 1.5rem !important;
                }

                .text-2xl {
                    font-size: 1.25rem !important;
                }

                .text-xl {
                    font-size: 1.125rem !important;
                }

                .text-lg {
                    font-size: 1rem !important;
                }

                /* Stats cards mobile */
                .stats-card {
                    padding: 0.75rem !important;
                }

                .stats-card .w-12.h-12 {
                    width: 2.5rem !important;
                    height: 2.5rem !important;
                }

                .stats-card .text-xl {
                    font-size: 1rem !important;
                }

                /* Main content mobile */
                main {
                    padding: 0.75rem !important;
                }

                /* Card mobile padding */
                .p-6 {
                    padding: 0.75rem !important;
                }

                .p-4 {
                    padding: 0.75rem !important;
                }

                /* Activity cards mobile */
                .activity-card .w-10.h-10 {
                    width: 2rem !important;
                    height: 2rem !important;
                }

                .activity-card .text-sm {
                    font-size: 0.75rem !important;
                }

                /* Profile avatar mobile */
                .profile-avatar {
                    width: 1.75rem !important;
                    height: 1.75rem !important;
                }

                /* Header toggle buttons mobile - matching notification button */
                .header-toggle-btn {
                    min-width: 40px !important;
                    min-height: 40px !important;
                    padding: 6px !important;
                }

                .header-toggle-icon {
                    font-size: 14px !important;
                }

                .fullscreen-icon {
                    font-size: 11px !important;
                }

                /* Form mobile improvements */
                .grid.grid-cols-1.md\\:grid-cols-2.gap-6 {
                    gap: 1rem !important;
                }

                /* Button mobile sizing */
                button {
                    padding: 0.75rem 1rem !important;
                    font-size: 0.875rem !important;
                }

                /* Input mobile sizing */
                input, select, textarea {
                    padding: 0.75rem !important;
                    font-size: 0.875rem !important;
                }

                /* Member cards mobile */
                .grid-cols-1.md\\:grid-cols-2.lg\\:grid-cols-3.xl\\:grid-cols-4 {
                    grid-template-columns: 1fr !important;
                }

                /* Pricing cards mobile */
                .grid-cols-1.lg\\:grid-cols-3 {
                    grid-template-columns: 1fr !important;
                    gap: 1rem;
                }
            }

            /* Custom scrollbar for main content */
            ::-webkit-scrollbar {
                width: 2px;
            }

            ::-webkit-scrollbar-track {
                background: #1F2937;
            }

            ::-webkit-scrollbar-thumb {
                background: #33AADA;
                border-radius: 3px;
            }

            ::-webkit-scrollbar-thumb:hover {
                background: #059669;
            }

            /* Sidebar Scrolling Styles */
            .sidebar-scroll {
                scrollbar-width: thin;
                scrollbar-color: #33AADA transparent;
            }

            .sidebar-scroll::-webkit-scrollbar {
                width: 6px;
            }

            .sidebar-scroll::-webkit-scrollbar-track {
                background: transparent;
            }

            /* Hidden scrollbar by default */
            .sidebar-scroll::-webkit-scrollbar-thumb {
                background: transparent;
                border-radius: 3px;
                transition: background-color 0.3s ease, opacity 0.3s ease;
            }

            .sidebar-scroll::-webkit-scrollbar-thumb:hover {
                background: rgba(107, 114, 128, 0.8);
            }

            /* Show scrollbar when actively scrolling - Dark theme */
            .sidebar-scroll.scrolling::-webkit-scrollbar-thumb {
                background: rgba(75, 85, 99, 0.8);
            }

            .sidebar-scroll.scrolling::-webkit-scrollbar-thumb:hover {
                background: rgba(107, 114, 128, 1);
            }

            /* Ensure smooth scrolling */
            .sidebar-scroll {
                scroll-behavior: smooth;
            }

            /* Mobile scrollbar styling */
            @media (max-width: 1023px) {
                .sidebar-scroll::-webkit-scrollbar {
                    width: 4px;
                }

                .sidebar-scroll::-webkit-scrollbar-thumb {
                    background: transparent;
                }

                .sidebar-scroll.scrolling::-webkit-scrollbar-thumb {
                    background: rgba(75, 85, 99, 0.6);
                }

                [data-theme="light"] .sidebar-scroll.scrolling::-webkit-scrollbar-thumb {
                    background: rgba(203, 213, 225, 0.6);
                }

                .sidebar-scroll {
                    scrollbar-width: thin;
                    scrollbar-color: transparent transparent;
                }
            }

            /* Sidebar layout improvements */
            #sidebar {
                display: flex;
                flex-direction: column;
                height: 100vh;
                max-height: 100vh;
            }

            /* Ensure navigation items have proper spacing for scrolling */
            #sidebar nav {
                min-height: 0; /* Allow flex item to shrink */
            }

            /* Additional scrolling visual enhancements */
            .sidebar-scroll.scrolling {
                /* Add subtle glow effect when scrolling */
                box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.1);
            }

            [data-theme="light"] .sidebar-scroll.scrolling {
                box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.05);
            }

            /* Firefox scrollbar styling */
            .sidebar-scroll {
                scrollbar-width: thin;
                scrollbar-color: transparent transparent;
            }

            .sidebar-scroll.scrolling {
                scrollbar-color: rgba(75, 85, 99, 0.8) transparent;
            }

            [data-theme="light"] .sidebar-scroll.scrolling {
                scrollbar-color: rgba(203, 213, 225, 0.8) transparent;
            }

            /* Ensure scrollbar area doesn't interfere with content */
            .sidebar-scroll {
                padding-right: 2px;
            }

            /* Fade effect for overflowing content */
            .sidebar-scroll::before,
            .sidebar-scroll::after {
                content: '';
                position: absolute;
                left: 0;
                right: 0;
                height: 20px;
                pointer-events: none;
                z-index: 1;
                transition: opacity 0.3s ease;
            }

            .sidebar-scroll::before {
                top: 0;
                background: linear-gradient(to bottom, #111827, transparent);
                opacity: 0;
            }

            .sidebar-scroll::after {
                bottom: 0;
                background: linear-gradient(to top, #111827, transparent);
                opacity: 0;
            }

            /* Show fade effects when scrolling */
            .sidebar-scroll.scrolling::before,
            .sidebar-scroll.scrolling::after {
                opacity: 1;
            }

            /* Ensure proper touch scrolling on mobile */
            .sidebar-scroll {
                -webkit-overflow-scrolling: touch;
                overscroll-behavior: contain;
            }

            /* Additional mobile optimizations */
            @media (max-width: 1023px) {
                .sidebar-scroll {
                    /* Improve touch scrolling performance */
                    transform: translateZ(0);
                    will-change: scroll-position;
                }

                /* Adjust padding for mobile scrolling */
                #sidebar nav {
                    padding-bottom: 2rem;
                }

                /* Improve touch targets for mobile */
                .nav-link {
                    padding: 0.875rem 0.75rem !important;
                    margin-bottom: 0.25rem;
                    min-height: 44px;
                    display: flex;
                    align-items: center;
                }

                /* Better mobile sidebar header */
                #sidebar .flex.items-center.justify-between {
                    padding: 1rem 1.5rem;
                    min-height: 64px;
                }

                /* Mobile menu button improvements */
                #open-sidebar, #close-sidebar {
                    padding: 0.75rem;
                    min-width: 44px;
                    min-height: 44px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }
            }

            /* Extra small mobile devices (480px and below) */
            @media (max-width: 480px) {
                /* Ultra compact layout */
                header {
                    padding: 0.5rem !important;
                }

                header h1 {
                    font-size: 1.25rem !important;
                }

                header p {
                    display: none !important;
                }

                /* Compact stats cards */
                .stats-card {
                    padding: 0.5rem !important;
                }

                .stats-card .text-3xl {
                    font-size: 1.25rem !important;
                }

                .stats-card .w-12.h-12 {
                    width: 2rem !important;
                    height: 2rem !important;
                }

                /* Ultra compact main content */
                main {
                    padding: 0.5rem !important;
                }

                /* Compact cards */
                .p-6, .p-4 {
                    padding: 0.5rem !important;
                }

                /* Compact activity items */
                .activity-card {
                    padding: 0.5rem !important;
                }

                .activity-card .space-x-4 {
                    gap: 0.5rem !important;
                }

                /* Compact form elements */
                input, select, textarea, button {
                    padding: 0.5rem !important;
                    font-size: 0.8rem !important;
                }

                /* Compact notification dropdown */
                #notification-dropdown {
                    width: calc(100vw - 1rem) !important;
                    right: 0.5rem !important;
                    left: 0.5rem !important;
                    top: 3.5rem !important;
                }

                /* Compact header toggle buttons */
                .header-toggle-btn {
                    min-width: 36px !important;
                    min-height: 36px !important;
                    padding: 4px !important;
                }

                .header-toggle-icon {
                    font-size: 12px !important;
                }

                .fullscreen-icon {
                    font-size: 10px !important;
                }

                /* Hide theme label completely */
                .flex.items-center.space-x-3 span {
                    display: none !important;
                }
            }

            /* Landscape mobile improvements */
            @media (max-width: 896px) and (orientation: landscape) {
                /* Optimize for landscape mobile */
                .grid-cols-1.md\\:grid-cols-2.lg\\:grid-cols-4 {
                    grid-template-columns: repeat(2, 1fr) !important;
                }

                .grid-cols-1.lg\\:grid-cols-3 {
                    grid-template-columns: repeat(2, 1fr) !important;
                }

                /* Compact header for landscape */
                header {
                    padding: 0.5rem 1rem !important;
                }

                main {
                    padding: 1rem !important;
                }
            }

            /* Large mobile/small tablet improvements */
            @media (min-width: 641px) and (max-width: 768px) {
                /* Optimize for large mobile screens */
                .grid-cols-1.md\\:grid-cols-2.lg\\:grid-cols-4 {
                    grid-template-columns: repeat(2, 1fr) !important;
                    gap: 1rem;
                }

                .grid-cols-1.lg\\:grid-cols-3 {
                    grid-template-columns: repeat(2, 1fr) !important;
                    gap: 1.5rem;
                }

                /* Show search on larger mobile */
                .hidden.sm\\:block {
                    display: block !important;
                }

                /* Better spacing for larger mobile */
                main {
                    padding: 1.5rem !important;
                }

                .stats-card {
                    padding: 1.25rem !important;
                }
            }

            /* Table responsive improvements */
            @media (max-width: 768px) {
                /* Make tables more mobile-friendly */
                table {
                    font-size: 0.875rem;
                }

                th, td {
                    padding: 0.5rem !important;
                    white-space: nowrap;
                }

                /* Hide less important columns on mobile */
                .table-mobile-hide {
                    display: none !important;
                }

                /* Stack table content vertically on very small screens */
                @media (max-width: 480px) {
                    .table-responsive {
                        display: block;
                        width: 100%;
                        overflow-x: auto;
                        -webkit-overflow-scrolling: touch;
                    }

                    .table-responsive table {
                        width: 100%;
                        margin-bottom: 0;
                    }
                }
            }

            /* Form responsive improvements */
            @media (max-width: 640px) {
                /* Stack form elements vertically */
                .form-grid {
                    display: grid !important;
                    grid-template-columns: 1fr !important;
                    gap: 1rem !important;
                }

                /* Full width form elements */
                .form-element {
                    width: 100% !important;
                }

                /* Better form spacing */
                .form-section {
                    margin-bottom: 1.5rem !important;
                }

                /* Form labels */
                label {
                    font-size: 0.875rem !important;
                    margin-bottom: 0.5rem !important;
                }

                /* Form inputs */
                input, select, textarea {
                    width: 100% !important;
                    padding: 0.75rem !important;
                    font-size: 0.875rem !important;
                    border-radius: 0.5rem !important;
                }

                /* Form buttons */
                .form-buttons {
                    display: flex !important;
                    flex-direction: column !important;
                    gap: 0.75rem !important;
                    width: 100% !important;
                }

                .form-buttons button {
                    width: 100% !important;
                    padding: 0.875rem !important;
                    font-size: 0.875rem !important;
                }
            }

            /* Card responsive improvements */
            @media (max-width: 640px) {
                /* Member cards */
                .member-card {
                    padding: 1rem !important;
                    margin-bottom: 1rem !important;
                }

                .member-card .flex {
                    flex-direction: column !important;
                    align-items: flex-start !important;
                    gap: 0.75rem !important;
                }

                .member-card .w-12.h-12 {
                    width: 2.5rem !important;
                    height: 2.5rem !important;
                }

                /* Pricing cards */
                .pricing-card {
                    padding: 1.5rem !important;
                    margin-bottom: 1rem !important;
                }

                .pricing-card .text-5xl {
                    font-size: 2rem !important;
                }

                .pricing-card .text-4xl {
                    font-size: 1.75rem !important;
                }

                /* Activity cards */
                .activity-card .flex {
                    flex-wrap: wrap !important;
                    gap: 0.5rem !important;
                }

                .activity-card .flex-1 {
                    min-width: 0 !important;
                    flex: 1 1 auto !important;
                }
            }

            /* Navigation responsive improvements */
            @media (max-width: 1023px) {
                /* Mobile sidebar improvements */
                #sidebar {
                    width: 280px !important;
                    max-width: 85vw !important;
                }

                /* Better mobile navigation */
                .nav-link {
                    font-size: 0.875rem !important;
                    padding: 0.875rem 1rem !important;
                    border-radius: 0.5rem !important;
                    margin-bottom: 0.25rem !important;
                }

                .nav-link i {
                    width: 1.25rem !important;
                    margin-right: 0.75rem !important;
                }

                /* Mobile sidebar header */
                #sidebar .gradient-text {
                    font-size: 1.25rem !important;
                }

                /* Mobile overlay improvements */
                #mobile-menu-overlay {
                    backdrop-filter: blur(4px) !important;
                    -webkit-backdrop-filter: blur(4px) !important;
                }
            }

            /* ===== DARK MODE RESPONSIVE FIXES ===== */

            /* Dark Mode Mobile Responsive (1023px and below) */
            @media (max-width: 1023px) {
                /* Dark mode sidebar mobile fixes */
                [data-theme="dark"] #sidebar {
                    background: var(--card-bg) !important;
                    border-right: 1px solid var(--border-primary) !important;
                }

                [data-theme="dark"] #sidebar::before {
                    display: none !important;
                }

                /* Dark mode navigation mobile */
                [data-theme="dark"] .nav-link {
                    color: var(--text-secondary) !important;
                    background: transparent !important;
                }

                [data-theme="dark"] .nav-link:hover {
                    color: var(--text-primary) !important;
                    background: var(--hover-bg) !important;
                }

                [data-theme="dark"] .nav-link.active {
                    color: var(--text-primary) !important;
                    background: var(--hover-bg) !important;
                    border-left: 3px solid #8554F5 !important;
                }

                /* Dark mode mobile overlay */
                [data-theme="dark"] #mobile-menu-overlay {
                    background: rgba(0, 0, 0, 0.7) !important;
                }
            }

            /* Dark Mode Tablet Responsive (768px and below) */
            @media (max-width: 768px) {
                /* Dark mode header mobile */
                [data-theme="dark"] header {
                    background: var(--card-bg) !important;
                    border-bottom: 1px solid var(--border-primary) !important;
                }

                [data-theme="dark"] header::before {
                    display: none !important;
                }

                /* Dark mode stats cards mobile */
                [data-theme="dark"] .stats-card {
                    background: var(--card-bg) !important;
                    border: 1px solid var(--border-primary) !important;
                }

                [data-theme="dark"] .stats-card::before,
                [data-theme="dark"] .stats-card::after {
                    opacity: 0.2 !important;
                }

                /* Dark mode main content mobile */
                [data-theme="dark"] main {
                    background: var(--bg-primary) !important;
                }

                /* Dark mode cards mobile */
                [data-theme="dark"] .rounded-xl:not(.stats-card):not(.activity-card):not(.quick-action-btn) {
                    background: var(--card-bg) !important;
                    border: 1px solid var(--border-primary) !important;
                }

                [data-theme="dark"] .rounded-xl:not(.stats-card):not(.activity-card):not(.quick-action-btn)::after {
                    display: none !important;
                }

                /* Dark mode activity cards mobile */
                [data-theme="dark"] .activity-card {
                    background: var(--hover-bg) !important;
                    border: 1px solid var(--border-primary) !important;
                }

                [data-theme="dark"] .activity-card::before {
                    display: none !important;
                }
            }

            /* Dark Mode Mobile Responsive (640px and below) */
            @media (max-width: 640px) {
                /* Dark mode notification dropdown mobile */
                [data-theme="dark"] #notification-dropdown {
                    background: var(--card-bg) !important;
                    border: 1px solid var(--border-primary) !important;
                    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3) !important;
                }

                [data-theme="dark"] #notification-dropdown::before {
                    opacity: 0.1 !important;
                }

                /* Dark mode notification items mobile */
                [data-theme="dark"] .notification-item {
                    background: var(--hover-bg) !important;
                    border: 1px solid var(--border-secondary) !important;
                }

                [data-theme="dark"] .notification-item::before {
                    display: none !important;
                }



                /* Dark mode profile avatar mobile */
                [data-theme="dark"] .profile-avatar {
                    background: var(--border-secondary) !important;
                    padding: 1px !important;
                }

                /* Dark mode form elements mobile */
                [data-theme="dark"] input,
                [data-theme="dark"] select,
                [data-theme="dark"] textarea {
                    background: var(--input-bg) !important;
                    border: 1px solid var(--border-secondary) !important;
                    color: var(--text-primary) !important;
                }

                [data-theme="dark"] input:focus,
                [data-theme="dark"] select:focus,
                [data-theme="dark"] textarea:focus {
                    border-color: #8554F5 !important;
                    box-shadow: 0 0 0 2px rgba(133, 84, 245, 0.2) !important;
                }

                /* Dark mode button mobile */
                [data-theme="dark"] button:not(.nav-link) {
                    background: var(--hover-bg) !important;
                    border: 1px solid var(--border-secondary) !important;
                    color: var(--text-primary) !important;
                }

                [data-theme="dark"] button:not(.nav-link):hover {
                    background: var(--border-secondary) !important;
                    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2) !important;
                }
            }

            /* Dark Mode Ultra Mobile (480px and below) */
            @media (max-width: 480px) {
                /* Dark mode ultra compact header */
                [data-theme="dark"] header {
                    background: var(--card-bg) !important;
                    border-bottom: 1px solid var(--border-primary) !important;
                    padding: 0.5rem !important;
                }

                /* Dark mode ultra compact stats */
                [data-theme="dark"] .stats-card {
                    background: var(--card-bg) !important;
                    border: 1px solid var(--border-primary) !important;
                    padding: 0.75rem !important;
                }

                [data-theme="dark"] .stats-card::before,
                [data-theme="dark"] .stats-card::after {
                    display: none !important;
                }

                /* Dark mode ultra compact main */
                [data-theme="dark"] main {
                    background: var(--bg-primary) !important;
                    padding: 0.5rem !important;
                }

                /* Dark mode ultra compact cards */
                [data-theme="dark"] .rounded-xl {
                    background: var(--card-bg) !important;
                    border: 1px solid var(--border-primary) !important;
                    padding: 0.75rem !important;
                }

                /* Dark mode ultra compact notification */
                [data-theme="dark"] #notification-dropdown {
                    background: var(--card-bg) !important;
                    border: 1px solid var(--border-primary) !important;
                    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.4) !important;
                }

                [data-theme="dark"] #notification-dropdown::before {
                    display: none !important;
                }


            }

            /* Dark Mode Landscape Mobile Fixes */
            @media (max-width: 896px) and (orientation: landscape) {
                [data-theme="dark"] header {
                    background: var(--card-bg) !important;
                    padding: 0.5rem 1rem !important;
                }

                [data-theme="dark"] main {
                    background: var(--bg-primary) !important;
                    padding: 1rem !important;
                }

                [data-theme="dark"] .stats-card {
                    background: var(--card-bg) !important;
                    border: 1px solid var(--border-primary) !important;
                }

                [data-theme="dark"] .stats-card::before,
                [data-theme="dark"] .stats-card::after {
                    opacity: 0.1 !important;
                }
            }

            /* Dark Mode Text Contrast Fixes for Mobile */
            @media (max-width: 768px) {
                /* Ensure proper text contrast in dark mode */
                [data-theme="dark"] .text-primary,
                [data-theme="dark"] h1,
                [data-theme="dark"] h2,
                [data-theme="dark"] h3,
                [data-theme="dark"] h4,
                [data-theme="dark"] h5,
                [data-theme="dark"] h6 {
                    color: var(--text-primary) !important;
                }

                [data-theme="dark"] .text-secondary,
                [data-theme="dark"] p {
                    color: var(--text-secondary) !important;
                }

                [data-theme="dark"] .text-muted,
                [data-theme="dark"] small {
                    color: var(--text-muted) !important;
                }

                /* Dark mode gradient text mobile fix */
                [data-theme="dark"] .gradient-text {
                    background: var(--gradient-primary-dark) !important;
                    -webkit-background-clip: text !important;
                    -webkit-text-fill-color: transparent !important;
                    background-clip: text !important;
                    opacity: 1 !important;
                }

                /* Dark mode link colors mobile */
                [data-theme="dark"] a {
                    color: #8554F5 !important;
                }

                [data-theme="dark"] a:hover {
                    color: #5D42EE !important;
                }
            }

            /* Dark Mode Component-Specific Mobile Fixes */
            @media (max-width: 640px) {
                /* Dark mode member cards mobile */
                [data-theme="dark"] .member-card {
                    background: var(--card-bg) !important;
                    border: 1px solid var(--border-primary) !important;
                }

                /* Dark mode pricing cards mobile */
                [data-theme="dark"] .pricing-card {
                    background: var(--card-bg) !important;
                    border: 1px solid var(--border-primary) !important;
                }

                /* Dark mode table mobile */
                [data-theme="dark"] table {
                    background: var(--card-bg) !important;
                    color: var(--text-primary) !important;
                }

                [data-theme="dark"] th {
                    background: var(--bg-secondary) !important;
                    color: var(--text-primary) !important;
                    border-bottom: 1px solid var(--border-primary) !important;
                }

                [data-theme="dark"] td {
                    border-bottom: 1px solid var(--border-secondary) !important;
                    color: var(--text-secondary) !important;
                }

                /* Dark mode form mobile */
                [data-theme="dark"] .form-section {
                    background: var(--card-bg) !important;
                    border: 1px solid var(--border-primary) !important;
                    border-radius: 0.75rem !important;
                    padding: 1rem !important;
                }

                [data-theme="dark"] label {
                    color: var(--text-secondary) !important;
                }

                /* Dark mode quick actions mobile */
                [data-theme="dark"] .quick-action-btn {
                    background: var(--hover-bg) !important;
                    border: 1px solid var(--border-secondary) !important;
                    color: var(--text-primary) !important;
                }

                [data-theme="dark"] .quick-action-btn:hover {
                    background: var(--border-secondary) !important;
                    border-color: #8554F5 !important;
                }
            }

            /* Dark Mode Scrollbar Mobile Fixes */
            @media (max-width: 1023px) {
                /* Dark mode sidebar scrollbar mobile */
                [data-theme="dark"] .sidebar-scroll::-webkit-scrollbar {
                    width: 4px !important;
                }

                [data-theme="dark"] .sidebar-scroll::-webkit-scrollbar-track {
                    background: transparent !important;
                }

                [data-theme="dark"] .sidebar-scroll::-webkit-scrollbar-thumb {
                    background: transparent !important;
                    transition: background-color 0.3s ease !important;
                }

                [data-theme="dark"] .sidebar-scroll.scrolling::-webkit-scrollbar-thumb {
                    background: rgba(75, 85, 99, 0.6) !important;
                }

                [data-theme="dark"] .sidebar-scroll.scrolling::-webkit-scrollbar-thumb:hover {
                    background: rgba(107, 114, 128, 0.8) !important;
                }

                /* Dark mode fade effects mobile */
                [data-theme="dark"] .sidebar-scroll::before {
                    background: linear-gradient(to bottom, var(--card-bg), transparent) !important;
                }

                [data-theme="dark"] .sidebar-scroll::after {
                    background: linear-gradient(to top, var(--card-bg), transparent) !important;
                }
            }

            /* Dark Mode CSS Variables Mobile Override */
            @media (max-width: 768px) {
                [data-theme="dark"] {
                    /* Ensure dark theme variables are properly applied on mobile */
                    --bg-primary: #111827 !important;
                    --bg-secondary: #1f2937 !important;
                    --bg-tertiary: #374151 !important;
                    --text-primary: #f9fafb !important;
                    --text-secondary: #d1d5db !important;
                    --text-muted: #9ca3af !important;
                    --border-primary: #374151 !important;
                    --border-secondary: #4b5563 !important;
                    --shadow-color: rgba(0, 0, 0, 0.3) !important;
                    --card-bg: #111827 !important;
                    --input-bg: #374151 !important;
                    --hover-bg: rgba(55, 65, 81, 0.5) !important;
                }

                /* Dark mode body mobile */
                [data-theme="dark"] body {
                    background-color: var(--bg-primary) !important;
                    color: var(--text-primary) !important;
                }
            }

            /* Dark Mode Focus States Mobile */
            @media (max-width: 640px) {
                [data-theme="dark"] *:focus-visible {
                    outline: 2px solid rgba(133, 84, 245, 0.6) !important;
                    outline-offset: 2px !important;
                }

                [data-theme="dark"] button:focus,
                [data-theme="dark"] input:focus,
                [data-theme="dark"] select:focus,
                [data-theme="dark"] textarea:focus {
                    box-shadow: 0 0 0 2px rgba(133, 84, 245, 0.3) !important;
                }
            }

            /* Dark Mode Animation Performance Mobile */
            @media (max-width: 768px) {
                /* Reduce animations on mobile for better performance in dark mode */
                [data-theme="dark"] .stats-card,
                [data-theme="dark"] .activity-card,
                [data-theme="dark"] .quick-action-btn {
                    transition: background-color 0.2s ease, border-color 0.2s ease !important;
                }

                [data-theme="dark"] .nav-link {
                    transition: background-color 0.2s ease, color 0.2s ease !important;
                }

                [data-theme="dark"] button {
                    transition: background-color 0.2s ease, box-shadow 0.2s ease !important;
                }
            }

            /* Dark Mode High Contrast Mobile (for accessibility) */
            @media (max-width: 640px) and (prefers-contrast: high) {
                [data-theme="dark"] {
                    --text-primary: #ffffff !important;
                    --text-secondary: #e5e7eb !important;
                    --border-primary: #6b7280 !important;
                    --border-secondary: #9ca3af !important;
                }

                [data-theme="dark"] .stats-card,
                [data-theme="dark"] .activity-card,
                [data-theme="dark"] .rounded-xl {
                    border-width: 2px !important;
                }
            }

            /* ===== SIDEBAR RESPONSIVE CONSISTENCY FIXES ===== */

            /* Base Sidebar Mobile Styles (Both Themes) */
            @media (max-width: 1023px) {
                /* Consistent sidebar dimensions and positioning */
                #sidebar {
                    width: 280px !important;
                    max-width: 85vw !important;
                    position: fixed !important;
                    top: 0 !important;
                    left: 0 !important;
                    bottom: 0 !important;
                    z-index: 50 !important;
                    transform: translateX(-100%) !important;
                    transition: transform 0.3s ease-in-out !important;
                }

                #sidebar.translate-x-0 {
                    transform: translateX(0) !important;
                }

                /* Consistent mobile overlay */
                #mobile-menu-overlay {
                    position: fixed !important;
                    top: 0 !important;
                    left: 0 !important;
                    right: 0 !important;
                    bottom: 0 !important;
                    z-index: 40 !important;
                    backdrop-filter: blur(4px) !important;
                    -webkit-backdrop-filter: blur(4px) !important;
                }

                /* Consistent sidebar header */
                #sidebar .flex.items-center.justify-between {
                    height: 64px !important;
                    padding: 1rem 1.5rem !important;
                    flex-shrink: 0 !important;
                }

                /* Consistent navigation container */
                #sidebar .sidebar-scroll {
                    flex: 1 !important;
                    overflow-y: auto !important;
                    -webkit-overflow-scrolling: touch !important;
                    overscroll-behavior: contain !important;
                }

                /* Consistent navigation links */
                .nav-link {
                    padding: 0.875rem 1rem !important;
                    margin-bottom: 0.25rem !important;
                    border-radius: 0.5rem !important;
                    font-size: 0.875rem !important;
                    min-height: 44px !important;
                    display: flex !important;
                    align-items: center !important;
                    transition: all 0.2s ease !important;
                }

                .nav-link i {
                    width: 1.25rem !important;
                    margin-right: 0.75rem !important;
                    flex-shrink: 0 !important;
                }

                /* Consistent mobile menu buttons */
                #open-sidebar, #close-sidebar {
                    padding: 0.75rem !important;
                    min-width: 44px !important;
                    min-height: 44px !important;
                    display: flex !important;
                    align-items: center !important;
                    justify-content: center !important;
                    border-radius: 0.5rem !important;
                    transition: all 0.2s ease !important;
                }
            }

            /* Light Mode Sidebar Mobile Responsive (1023px and below) */
            @media (max-width: 1023px) {
                /* Light mode sidebar mobile styling */
                [data-theme="light"] #sidebar {
                    background: var(--card-bg) !important;
                    border-right: 1px solid var(--border-primary) !important;
                    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1) !important;
                }

                /* Light mode navigation mobile */
                [data-theme="light"] .nav-link {
                    color: var(--text-secondary) !important;
                    background: transparent !important;
                }

                [data-theme="light"] .nav-link:hover {
                    color: var(--text-primary) !important;
                    background: var(--hover-bg) !important;
                }

                [data-theme="light"] .nav-link.active {
                    color: var(--text-primary) !important;
                    background: var(--hover-bg) !important;
                    border-left: 3px solid #8554F5 !important;
                }

                /* Light mode mobile overlay */
                [data-theme="light"] #mobile-menu-overlay {
                    background: rgba(0, 0, 0, 0.3) !important;
                }

                /* Light mode sidebar header mobile */
                [data-theme="light"] #sidebar .flex.items-center.justify-between {
                    border-bottom: 1px solid var(--border-primary) !important;
                    background: var(--card-bg) !important;
                }

                /* Light mode close button */
                [data-theme="light"] #close-sidebar {
                    color: var(--text-muted) !important;
                }

                [data-theme="light"] #close-sidebar:hover {
                    color: var(--text-primary) !important;
                    background: var(--hover-bg) !important;
                }

                /* Light mode open button */
                [data-theme="light"] #open-sidebar {
                    color: var(--text-muted) !important;
                }

                [data-theme="light"] #open-sidebar:hover {
                    color: var(--text-primary) !important;
                    background: var(--hover-bg) !important;
                }
            }

            /* Dark Mode Sidebar Mobile Responsive (1023px and below) - Enhanced */
            @media (max-width: 1023px) {
                /* Dark mode sidebar mobile styling */
                [data-theme="dark"] #sidebar {
                    background: var(--card-bg) !important;
                    border-right: 1px solid var(--border-primary) !important;
                    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3) !important;
                }

                /* Remove complex gradients on mobile for dark mode */
                [data-theme="dark"] #sidebar::before {
                    display: none !important;
                }

                /* Dark mode navigation mobile */
                [data-theme="dark"] .nav-link {
                    color: var(--text-secondary) !important;
                    background: transparent !important;
                }

                [data-theme="dark"] .nav-link:hover {
                    color: var(--text-primary) !important;
                    background: var(--hover-bg) !important;
                }

                [data-theme="dark"] .nav-link.active {
                    color: var(--text-primary) !important;
                    background: var(--hover-bg) !important;
                    border-left: 3px solid #8554F5 !important;
                }

                /* Dark mode mobile overlay */
                [data-theme="dark"] #mobile-menu-overlay {
                    background: rgba(0, 0, 0, 0.7) !important;
                }

                /* Dark mode sidebar header mobile */
                [data-theme="dark"] #sidebar .flex.items-center.justify-between {
                    border-bottom: 1px solid var(--border-primary) !important;
                    background: var(--card-bg) !important;
                }

                /* Dark mode close button */
                [data-theme="dark"] #close-sidebar {
                    color: var(--text-muted) !important;
                }

                [data-theme="dark"] #close-sidebar:hover {
                    color: var(--text-primary) !important;
                    background: var(--hover-bg) !important;
                }

                /* Dark mode open button */
                [data-theme="dark"] #open-sidebar {
                    color: var(--text-muted) !important;
                }

                [data-theme="dark"] #open-sidebar:hover {
                    color: var(--text-primary) !important;
                    background: var(--hover-bg) !important;
                }
            }

            /* Sidebar Scrollbar Consistency (Both Themes) */
            @media (max-width: 1023px) {
                /* Base scrollbar styling */
                .sidebar-scroll::-webkit-scrollbar {
                    width: 4px !important;
                }

                .sidebar-scroll::-webkit-scrollbar-track {
                    background: transparent !important;
                }

                .sidebar-scroll::-webkit-scrollbar-thumb {
                    background: transparent !important;
                    border-radius: 2px !important;
                    transition: background-color 0.3s ease !important;
                }

                /* Light mode scrollbar */
                [data-theme="light"] .sidebar-scroll.scrolling::-webkit-scrollbar-thumb {
                    background: rgba(203, 213, 225, 0.6) !important;
                }

                [data-theme="light"] .sidebar-scroll.scrolling::-webkit-scrollbar-thumb:hover {
                    background: rgba(100, 116, 139, 0.8) !important;
                }

                /* Dark mode scrollbar */
                [data-theme="dark"] .sidebar-scroll.scrolling::-webkit-scrollbar-thumb {
                    background: rgba(75, 85, 99, 0.6) !important;
                }

                [data-theme="dark"] .sidebar-scroll.scrolling::-webkit-scrollbar-thumb:hover {
                    background: rgba(107, 114, 128, 0.8) !important;
                }

                /* Firefox scrollbar support */
                .sidebar-scroll {
                    scrollbar-width: thin !important;
                    scrollbar-color: transparent transparent !important;
                }

                [data-theme="light"] .sidebar-scroll.scrolling {
                    scrollbar-color: rgba(203, 213, 225, 0.6) transparent !important;
                }

                [data-theme="dark"] .sidebar-scroll.scrolling {
                    scrollbar-color: rgba(75, 85, 99, 0.6) transparent !important;
                }
            }

            /* Sidebar Fade Effects Consistency (Both Themes) */
            @media (max-width: 1023px) {
                /* Base fade effects */
                .sidebar-scroll {
                    position: relative !important;
                }

                .sidebar-scroll::before,
                .sidebar-scroll::after {
                    content: '' !important;
                    position: absolute !important;
                    left: 0 !important;
                    right: 0 !important;
                    height: 20px !important;
                    pointer-events: none !important;
                    z-index: 1 !important;
                    transition: opacity 0.3s ease !important;
                    opacity: 0 !important;
                }

                .sidebar-scroll::before {
                    top: 0 !important;
                }

                .sidebar-scroll::after {
                    bottom: 0 !important;
                }

                /* Light mode fade effects */
                [data-theme="light"] .sidebar-scroll::before {
                    background: linear-gradient(to bottom, var(--card-bg), transparent) !important;
                }

                [data-theme="light"] .sidebar-scroll::after {
                    background: linear-gradient(to top, var(--card-bg), transparent) !important;
                }

                /* Dark mode fade effects */
                [data-theme="dark"] .sidebar-scroll::before {
                    background: linear-gradient(to bottom, var(--card-bg), transparent) !important;
                }

                [data-theme="dark"] .sidebar-scroll::after {
                    background: linear-gradient(to top, var(--card-bg), transparent) !important;
                }

                /* Show fade effects when scrolling */
                .sidebar-scroll.scrolling::before,
                .sidebar-scroll.scrolling::after {
                    opacity: 1 !important;
                }

                /* Scrolling glow effects */
                [data-theme="light"] .sidebar-scroll.scrolling {
                    box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.05) !important;
                }

                [data-theme="dark"] .sidebar-scroll.scrolling {
                    box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.1) !important;
                }
            }

            /* Tablet Responsive Sidebar (768px and below) */
            @media (max-width: 768px) {
                /* Consistent sidebar width for tablet */
                #sidebar {
                    width: 280px !important;
                    max-width: 80vw !important;
                }

                /* Both themes sidebar styling */
                [data-theme="light"] #sidebar,
                [data-theme="dark"] #sidebar {
                    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15) !important;
                }

                /* Navigation link tablet sizing */
                .nav-link {
                    padding: 0.75rem 0.875rem !important;
                    font-size: 0.875rem !important;
                    min-height: 42px !important;
                }

                .nav-link i {
                    width: 1.125rem !important;
                    margin-right: 0.625rem !important;
                }

                /* Sidebar header tablet */
                #sidebar .flex.items-center.justify-between {
                    height: 60px !important;
                    padding: 0.875rem 1.25rem !important;
                }

                #sidebar .gradient-text {
                    font-size: 1.125rem !important;
                }

                /* Mobile menu buttons tablet */
                #open-sidebar, #close-sidebar {
                    padding: 0.625rem !important;
                    min-width: 42px !important;
                    min-height: 42px !important;
                }
            }

            /* Mobile Responsive Sidebar (640px and below) */
            @media (max-width: 640px) {
                /* Consistent sidebar width for mobile */
                #sidebar {
                    width: 280px !important;
                    max-width: 90vw !important;
                }

                /* Enhanced shadow for mobile */
                [data-theme="light"] #sidebar {
                    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.2) !important;
                }

                [data-theme="dark"] #sidebar {
                    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.4) !important;
                }

                /* Compact navigation for mobile */
                .nav-link {
                    padding: 0.625rem 0.75rem !important;
                    font-size: 0.8125rem !important;
                    min-height: 40px !important;
                }

                .nav-link i {
                    width: 1rem !important;
                    margin-right: 0.5rem !important;
                    font-size: 0.875rem !important;
                }

                /* Compact sidebar header */
                #sidebar .flex.items-center.justify-between {
                    height: 56px !important;
                    padding: 0.75rem 1rem !important;
                }

                #sidebar .gradient-text {
                    font-size: 1rem !important;
                }

                /* Compact mobile menu buttons */
                #open-sidebar, #close-sidebar {
                    padding: 0.5rem !important;
                    min-width: 40px !important;
                    min-height: 40px !important;
                }

                /* Compact scrollbar for mobile */
                .sidebar-scroll::-webkit-scrollbar {
                    width: 3px !important;
                }

                /* Reduced fade effects for mobile */
                .sidebar-scroll::before,
                .sidebar-scroll::after {
                    height: 15px !important;
                }
            }

            /* Ultra Mobile Responsive Sidebar (480px and below) */
            @media (max-width: 480px) {
                /* Ultra compact sidebar */
                #sidebar {
                    width: 260px !important;
                    max-width: 95vw !important;
                }

                /* Ultra compact navigation */
                .nav-link {
                    padding: 0.5rem 0.625rem !important;
                    font-size: 0.75rem !important;
                    min-height: 36px !important;
                }

                .nav-link i {
                    width: 0.875rem !important;
                    margin-right: 0.375rem !important;
                    font-size: 0.75rem !important;
                }

                /* Ultra compact header */
                #sidebar .flex.items-center.justify-between {
                    height: 52px !important;
                    padding: 0.625rem 0.875rem !important;
                }

                #sidebar .gradient-text {
                    font-size: 0.875rem !important;
                }

                /* Ultra compact buttons */
                #open-sidebar, #close-sidebar {
                    padding: 0.375rem !important;
                    min-width: 36px !important;
                    min-height: 36px !important;
                }

                /* Minimal scrollbar */
                .sidebar-scroll::-webkit-scrollbar {
                    width: 2px !important;
                }

                /* Minimal fade effects */
                .sidebar-scroll::before,
                .sidebar-scroll::after {
                    height: 10px !important;
                }
            }

            /* ===== SIDEBAR MOBILE FUNCTIONALITY FIXES ===== */

            /* Critical: Ensure sidebar works on mobile - Override all conflicting styles */
            @media (max-width: 1023px) {
                /* Force sidebar to be hidden by default on mobile */
                #sidebar {
                    position: fixed !important;
                    top: 0 !important;
                    left: 0 !important;
                    bottom: 0 !important;
                    width: 280px !important;
                    max-width: 85vw !important;
                    z-index: 50 !important;
                    transform: translateX(-100%) !important;
                    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
                    display: flex !important;
                    flex-direction: column !important;
                }

                /* Show sidebar when translate-x-0 class is added */
                #sidebar.translate-x-0 {
                    transform: translateX(0) !important;
                }

                /* Ensure sidebar is never shown by default on mobile */
                #sidebar:not(.translate-x-0) {
                    transform: translateX(-100%) !important;
                }

                /* Mobile overlay critical fixes */
                #mobile-menu-overlay {
                    position: fixed !important;
                    top: 0 !important;
                    left: 0 !important;
                    right: 0 !important;
                    bottom: 0 !important;
                    z-index: 40 !important;
                    backdrop-filter: blur(4px) !important;
                    -webkit-backdrop-filter: blur(4px) !important;
                    transition: opacity 0.3s ease, visibility 0.3s ease !important;
                }

                /* Hidden overlay */
                #mobile-menu-overlay.hidden {
                    opacity: 0 !important;
                    visibility: hidden !important;
                    pointer-events: none !important;
                }

                /* Visible overlay */
                #mobile-menu-overlay:not(.hidden) {
                    opacity: 1 !important;
                    visibility: visible !important;
                    pointer-events: auto !important;
                }

                /* Ensure mobile menu buttons are visible */
                #open-sidebar {
                    display: flex !important;
                    align-items: center !important;
                    justify-content: center !important;
                    padding: 0.75rem !important;
                    min-width: 44px !important;
                    min-height: 44px !important;
                    border-radius: 0.5rem !important;
                    transition: all 0.2s ease !important;
                }

                #close-sidebar {
                    display: flex !important;
                    align-items: center !important;
                    justify-content: center !important;
                    padding: 0.75rem !important;
                    min-width: 44px !important;
                    min-height: 44px !important;
                    border-radius: 0.5rem !important;
                    transition: all 0.2s ease !important;
                }

                /* Prevent body scroll when sidebar is open */
                body.sidebar-open {
                    overflow: hidden !important;
                }

                /* Ensure sidebar content is scrollable */
                .sidebar-scroll {
                    flex: 1 !important;
                    overflow-y: auto !important;
                    -webkit-overflow-scrolling: touch !important;
                    overscroll-behavior: contain !important;
                }
            }

            /* Theme-specific mobile styling (non-conflicting) */
            @media (max-width: 1023px) {
                /* Light theme mobile sidebar */
                [data-theme="light"] #sidebar {
                    background: var(--card-bg) !important;
                    border-right: 1px solid var(--border-primary) !important;
                    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1) !important;
                }

                [data-theme="light"] #mobile-menu-overlay:not(.hidden) {
                    background: rgba(0, 0, 0, 0.3) !important;
                }

                [data-theme="light"] #open-sidebar {
                    color: var(--text-muted) !important;
                }

                [data-theme="light"] #open-sidebar:hover {
                    color: var(--text-primary) !important;
                    background: var(--hover-bg) !important;
                }

                [data-theme="light"] #close-sidebar {
                    color: var(--text-muted) !important;
                }

                [data-theme="light"] #close-sidebar:hover {
                    color: var(--text-primary) !important;
                    background: var(--hover-bg) !important;
                }

                /* Dark theme mobile sidebar */
                [data-theme="dark"] #sidebar {
                    background: var(--card-bg) !important;
                    border-right: 1px solid var(--border-primary) !important;
                    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3) !important;
                }

                [data-theme="dark"] #mobile-menu-overlay:not(.hidden) {
                    background: rgba(0, 0, 0, 0.7) !important;
                }

                [data-theme="dark"] #open-sidebar {
                    color: var(--text-muted) !important;
                }

                [data-theme="dark"] #open-sidebar:hover {
                    color: var(--text-primary) !important;
                    background: var(--hover-bg) !important;
                }

                [data-theme="dark"] #close-sidebar {
                    color: var(--text-muted) !important;
                }

                [data-theme="dark"] #close-sidebar:hover {
                    color: var(--text-primary) !important;
                    background: var(--hover-bg) !important;
                }

                /* Remove conflicting pseudo-elements on mobile */
                [data-theme="light"] #sidebar::before,
                [data-theme="light"] #sidebar::after,
                [data-theme="dark"] #sidebar::before,
                [data-theme="dark"] #sidebar::after {
                    display: none !important;
                }
            }

            /* Performance optimizations for mobile sidebar */
            @media (max-width: 1023px) {
                #sidebar {
                    will-change: transform !important;
                    backface-visibility: hidden !important;
                    -webkit-backface-visibility: hidden !important;
                }

                #mobile-menu-overlay {
                    will-change: opacity !important;
                }

                .sidebar-scroll {
                    will-change: scroll-position !important;
                    transform: translateZ(0) !important;
                }
            }

            /* ===== MOBILE MENU BUTTON VISIBILITY FIXES ===== */

            /* Ensure hamburger menu button is always visible on mobile */
            @media (max-width: 1023px) {
                #open-sidebar {
                    display: flex !important;
                    opacity: 1 !important;
                    visibility: visible !important;
                    pointer-events: auto !important;
                }

                /* Hide on desktop */
                @media (min-width: 1024px) {
                    #open-sidebar {
                        display: none !important;
                    }

                    #close-sidebar {
                        display: none !important;
                    }
                }
            }

            /* Final mobile sidebar polish */
            @media (max-width: 1023px) {
                /* Ensure smooth animations */
                #sidebar {
                    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
                }

                #mobile-menu-overlay {
                    transition: opacity 0.3s ease, visibility 0.3s ease !important;
                }

                /* Ensure proper touch targets */
                #open-sidebar,
                #close-sidebar {
                    cursor: pointer !important;
                    user-select: none !important;
                    -webkit-user-select: none !important;
                    -webkit-tap-highlight-color: transparent !important;
                }
            }

            /* ===== COMPACT LAYOUT GLOBAL STYLES ===== */

            /* Compact spacing for all screen sizes */
            .compact-layout {
                /* Reduce default margins and padding */
                --compact-spacing-xs: 0.25rem;  /* 4px */
                --compact-spacing-sm: 0.5rem;   /* 8px */
                --compact-spacing-md: 0.75rem;  /* 12px */
                --compact-spacing-lg: 1rem;     /* 16px */
                --compact-spacing-xl: 1.5rem;   /* 24px */
            }

            /* Apply compact spacing globally */
            body {
                --compact-spacing-xs: 0.25rem;
                --compact-spacing-sm: 0.5rem;
                --compact-spacing-md: 0.75rem;
                --compact-spacing-lg: 1rem;
                --compact-spacing-xl: 1.5rem;
            }

            /* Compact stats cards */
            .stats-card {
                padding: var(--compact-spacing-md) !important;
                margin-bottom: var(--compact-spacing-sm) !important;
            }

            /* Compact activity cards */
            .activity-card {
                padding: var(--compact-spacing-sm) !important;
                margin-bottom: var(--compact-spacing-xs) !important;
            }

            /* Compact quick action buttons */
            .quick-action-btn {
                padding: var(--compact-spacing-sm) !important;
                margin-bottom: var(--compact-spacing-xs) !important;
            }

            /* Compact navigation links */
            .nav-link {
                padding: var(--compact-spacing-xs) var(--compact-spacing-sm) !important;
                margin-bottom: var(--compact-spacing-xs) !important;
            }

            /* Compact grid gaps */
            .grid {
                gap: var(--compact-spacing-md) !important;
            }

            /* Compact section spacing */
            .section {
                margin-bottom: var(--compact-spacing-lg) !important;
            }

            /* Compact header */
            header {
                padding: var(--compact-spacing-sm) var(--compact-spacing-md) !important;
            }

            /* Compact main content */
            main {
                padding: var(--compact-spacing-sm) var(--compact-spacing-md) !important;
            }

            /* ===== RESPONSIVE COMPACT LAYOUT ===== */

            /* Desktop Compact (1024px+) */
            @media (min-width: 1024px) {
                .stats-card {
                    padding: 1rem !important;
                }

                .activity-card {
                    padding: 0.75rem !important;
                }

                .quick-action-btn {
                    padding: 0.75rem !important;
                }

                main {
                    padding: 1rem 1.5rem !important;
                }

                header {
                    padding: 0.75rem 1.5rem !important;
                }

                .grid {
                    gap: 1rem !important;
                }
            }

            /* Tablet Compact (768px-1023px) */
            @media (max-width: 1023px) and (min-width: 768px) {
                .stats-card {
                    padding: 0.75rem !important;
                }

                .activity-card {
                    padding: 0.5rem !important;
                }

                .quick-action-btn {
                    padding: 0.5rem !important;
                }

                main {
                    padding: 0.75rem !important;
                }

                header {
                    padding: 0.5rem 0.75rem !important;
                }

                .grid {
                    gap: 0.75rem !important;
                }

                /* Compact navigation for tablet */
                .nav-link {
                    padding: 0.375rem 0.5rem !important;
                    font-size: 0.875rem !important;
                }

                /* Compact sidebar header for tablet */
                #sidebar .flex.items-center.justify-between {
                    height: 3rem !important;
                    padding: 0.5rem 1rem !important;
                }
            }

            /* Mobile Compact (640px-767px) */
            @media (max-width: 767px) and (min-width: 640px) {
                .stats-card {
                    padding: 0.5rem !important;
                }

                .activity-card {
                    padding: 0.375rem !important;
                }

                .quick-action-btn {
                    padding: 0.375rem !important;
                }

                main {
                    padding: 0.5rem !important;
                }

                header {
                    padding: 0.375rem 0.5rem !important;
                }

                .grid {
                    gap: 0.5rem !important;
                }

                /* Ultra compact navigation for mobile */
                .nav-link {
                    padding: 0.25rem 0.375rem !important;
                    font-size: 0.8125rem !important;
                }

                /* Ultra compact sidebar header for mobile */
                #sidebar .flex.items-center.justify-between {
                    height: 2.5rem !important;
                    padding: 0.375rem 0.75rem !important;
                }
            }

            /* Ultra Mobile Compact (below 640px) */
            @media (max-width: 639px) {
                .stats-card {
                    padding: 0.375rem !important;
                }

                .activity-card {
                    padding: 0.25rem !important;
                }

                .quick-action-btn {
                    padding: 0.25rem !important;
                }

                main {
                    padding: 0.375rem !important;
                }

                header {
                    padding: 0.25rem 0.375rem !important;
                }

                .grid {
                    gap: 0.375rem !important;
                }

                /* Minimal navigation for ultra mobile */
                .nav-link {
                    padding: 0.1875rem 0.25rem !important;
                    font-size: 0.75rem !important;
                }

                /* Minimal sidebar header for ultra mobile */
                #sidebar .flex.items-center.justify-between {
                    height: 2.25rem !important;
                    padding: 0.25rem 0.5rem !important;
                }

                /* Compact text sizes for ultra mobile */
                h1 {
                    font-size: 1.125rem !important;
                }

                h2 {
                    font-size: 1rem !important;
                }

                h3 {
                    font-size: 0.875rem !important;
                }
            }

            /* ===== COMPACT FORM STYLING ===== */

            /* Compact form elements for all screen sizes */
            .form-section {
                margin-bottom: var(--compact-spacing-lg) !important;
                padding: var(--compact-spacing-md) !important;
            }

            .form-grid {
                gap: var(--compact-spacing-md) !important;
            }

            label {
                margin-bottom: var(--compact-spacing-xs) !important;
                font-size: 0.875rem !important;
            }

            input, select, textarea {
                padding: var(--compact-spacing-sm) !important;
                margin-bottom: var(--compact-spacing-sm) !important;
                font-size: 0.875rem !important;
            }

            button {
                padding: var(--compact-spacing-sm) var(--compact-spacing-md) !important;
                font-size: 0.875rem !important;
            }

            /* Compact form responsive adjustments */
            @media (max-width: 768px) {
                .form-section {
                    margin-bottom: var(--compact-spacing-md) !important;
                    padding: var(--compact-spacing-sm) !important;
                }

                .form-grid {
                    gap: var(--compact-spacing-sm) !important;
                }

                label {
                    font-size: 0.8125rem !important;
                }

                input, select, textarea {
                    padding: var(--compact-spacing-xs) !important;
                    font-size: 0.8125rem !important;
                }

                button {
                    padding: var(--compact-spacing-xs) var(--compact-spacing-sm) !important;
                    font-size: 0.8125rem !important;
                }
            }

            @media (max-width: 640px) {
                .form-section {
                    margin-bottom: var(--compact-spacing-sm) !important;
                    padding: var(--compact-spacing-xs) !important;
                }

                .form-grid {
                    gap: var(--compact-spacing-xs) !important;
                }

                label {
                    font-size: 0.75rem !important;
                }

                input, select, textarea {
                    padding: 0.1875rem !important;
                    font-size: 0.75rem !important;
                }

                button {
                    padding: 0.1875rem var(--compact-spacing-xs) !important;
                    font-size: 0.75rem !important;
                }
            }

            /* ===== COMPACT NOTIFICATION STYLING ===== */

            /* Compact notification dropdown */
            #notification-dropdown {
                padding: var(--compact-spacing-sm) !important;
            }

            #notification-dropdown .p-3,
            #notification-dropdown .p-4 {
                padding: var(--compact-spacing-sm) !important;
            }

            .notification-item {
                padding: var(--compact-spacing-xs) !important;
                margin-bottom: var(--compact-spacing-xs) !important;
            }

            /* Responsive notification adjustments */
            @media (max-width: 640px) {
                #notification-dropdown {
                    padding: var(--compact-spacing-xs) !important;
                }

                #notification-dropdown .p-3,
                #notification-dropdown .p-4 {
                    padding: var(--compact-spacing-xs) !important;
                }
            }

            /* ===== COMPACT THEME CONSISTENCY ===== */

            /* Ensure compact layout works in both themes */
            [data-theme="light"] .stats-card,
            [data-theme="dark"] .stats-card {
                padding: var(--compact-spacing-md) !important;
            }

            [data-theme="light"] .activity-card,
            [data-theme="dark"] .activity-card {
                padding: var(--compact-spacing-sm) !important;
            }

            [data-theme="light"] .quick-action-btn,
            [data-theme="dark"] .quick-action-btn {
                padding: var(--compact-spacing-sm) !important;
            }

            [data-theme="light"] .nav-link,
            [data-theme="dark"] .nav-link {
                padding: var(--compact-spacing-xs) var(--compact-spacing-sm) !important;
            }

            /* Compact spacing for both themes on mobile */
            @media (max-width: 768px) {
                [data-theme="light"] main,
                [data-theme="dark"] main {
                    padding: var(--compact-spacing-sm) !important;
                }

                [data-theme="light"] header,
                [data-theme="dark"] header {
                    padding: var(--compact-spacing-xs) var(--compact-spacing-sm) !important;
                }

                [data-theme="light"] .stats-card,
                [data-theme="dark"] .stats-card {
                    padding: var(--compact-spacing-sm) !important;
                }
            }

            /* Gym Blue Color Enhancements */
            .text-gym-blue {
                color: #337ADE;
            }

            .bg-gym-blue {
                background-color: #337ADE;
            }

            .border-gym-blue {
                border-color: #337ADE;
            }

            .hover\\:bg-gym-blue\\/80:hover {
                background-color: rgba(51, 122, 222, 0.8);
            }

            .hover\\:bg-gym-blue\\/30:hover {
                background-color: rgba(51, 122, 222, 0.3);
            }

            .hover\\:border-gym-blue\\/50:hover {
                border-color: rgba(51, 122, 222, 0.5);
            }

            /* Enhanced hover effects with gym-blue */
            .hover\\:text-gym-blue:hover {
                color: #337ADE;
                transition: color 0.2s ease;
            }

            /* Focus states for accessibility */
            .focus\\:ring-gym-blue:focus {
                --tw-ring-color: rgba(51, 122, 222, 0.5);
            }

            /* Gradient effects with gym-blue */
            .bg-gradient-to-r.from-gym-blue {
                background-image: linear-gradient(to right, #337ADE, var(--tw-gradient-to));
            }

            /* Box shadow effects */
            .shadow-gym-blue {
                box-shadow: 0 4px 14px 0 rgba(51, 122, 222, 0.15);
            }

            /* Ensure slideDown animation is defined if not already */
            @keyframes slideDown {
                from {
                    opacity: 0;
                    transform: translateY(-10px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>